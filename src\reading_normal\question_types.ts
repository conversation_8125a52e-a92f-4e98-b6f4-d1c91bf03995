// Common prompt patterns
import { DifficultyLevel } from '../types.ts';

const commonPrompts = {
    difficultyLevels: {
        Easy: {
            length: '- The passage is relatively short, around 70-90 words.',
            passage: '- The passage uses simple to moderate words that are understandable by a highschooler.',
            choices: '- The incorrect answer choices are not too tricky to eliminate. One way to do this is to only uses the first few incorrect choice categories.'
        },
        Medium: {
            length: '- The passage is moderately long, around 80-100 words.',
            passage: '- The passage uses scientific words that require senior-highschool to first-year-university reading level to comprehend.\n' +
                    '- The passage does not elaborate on vague terms, but still provide enough information to answer the question.',
            choices: '- The lengths of answer choices are moderate.\n' +
                    '- The incorrect answer choices are somewhat tricky to eliminate, requiring the students to read carefully.'
        },
        Hard: {
            length: '- The passage is moderately long, around 80-100 words.',
            passage: '- The passage uses formal scientific words, similar to those in actual studies.\n' +
                    '- The passage does not elaborate on vague terms, but still provide enough information to answer the question.\n' +
                    '- The passage uses scientific names (latin names) for species and long nouns, if applicable.',
            choices: '- The incorrect answer choices are tricky to eliminate, requiring the students to read extremely carefully to not get tricked.' + "\n" +
                    '- 1 incorrect answer choice is very similar to the correct answer choice. However, make sure that it is still incorrect.'
        }
    }
};

// Base Question class
class ReadingQuestion {
    type: string;
    topic: string;
    difficulty: DifficultyLevel;
    prompts: {
        Easy: string;
        Medium: string;
        Hard: string;
    };
    passageStructure: string;
    question: string;
    answerChoiceGuide: string;
    overallDifficulty: string;
    outputFormat: (explanationInstructions: string) => string;
    example?: string;
    explanationInstructions?: string;
    systemInstructions?: string;
    intro?: string
    evidenceType?: string;

    constructor(type: string, topic: string, difficulty: DifficultyLevel) {
        this.type = type;
        this.topic = topic.toLowerCase();
        this.difficulty = difficulty;
        this.prompts = {
            Easy: '',
            Medium: '',
            Hard: ''
        };
        this.passageStructure = "# Instructions\n" +
                        'Follow these guidelines:\n' +
                        '## Passage Structure:\n' +
                        '- Except for human names, all other names (e.g. scientific species names, novel names, poem names, exhibition names, song names, architecture names) are italic. This should be marked by an asterisk (*) at the start and end of the name. Concepts that have normal English names are not italic.\n' +
                        '- The passage topic is one of Social Science, Natural Science, or Humanities.\n' +
                        '- Examples include Scientific Research & Studies, Social Science, Economic Analysis, Environmental Science, Human Behavior & Psychology, Literature, and Art Interpretation.\n' +
                        '- The passage refers to actual real studies, scientific articles, or books. Therefore, it also includes the actual researcher\'s or author\'s name. Usually, it also includes the actual published year. Do not make these up. Do not state where the study was published.\n' +
                        '- Some common passage structures include:\n' +
                        '  - Presentation of a Phenomenon + Evaluation of Explanations (Example: A passage about asteroid 6478 Gault presents mass loss, a natural phenomenon, refutes two explanations, and then present an alternative theory​)\n' +
                        '  - Scientific Hypothesis + Study Results (Example: A passage about consumer decision-making presents a study on why people choose green energy plans, and then shows why calculation-based decision-makers avoided the green plan despite its financial benefits​)\n' +
                        '  - Contrast Between Two Cases (Example: The music industry passage contrasts two musicians\' experiences with a "pay-as-you-wish" pricing model, leading to an explanation about why one succeeded while the other failed​)\n' +
                        '  - Historical Evidence + Conclusion (Example: The passage on 19th-century Chinese trade networks introduces archaeological findings and connects them to global trade routes, then show their correlations to the broader economic impact​)';
        this.question = "## Question:\n";
        this.answerChoiceGuide = "## Answer Choices:\n" +
            "- Generate four answer choices (A, B, C, and D).\n" +
            "- One answer should be correct and supported by the passage.\n" +
            "- The other three should be incorrect. Incorrect choices typically fall into one of these categories, ranked from easy to hard:\n" +
            "- Contradicts the passage\n" +
            "- Off-topic\n" +
            "- Too narrow\n" +
            "- Too broad (e.g. the passage discusses one medicine while the choice refers to all medicine.)\n" +
            "- Too extreme (e.g. they include words such as never or always)\n" +
            "- Half-right, half-wrong\n" +
            "- Could be true but not enough information\n" +
            "- True in the real world but is not stated in the passage.\n" +
            "- Use different incorrect choice types for each incorrect choice.\n" +
            "- Usually, 2 incorrect choices are easier to eliminate, while the last incorrect choice is harder to distinguish. However, this is not a strict requirement.";
        this.overallDifficulty = "## Overall Difficulty:\n" +
            "- The question and the passage should be challenging but fair, suitable for high school students taking the SAT.\n" +
            "- Ensure that the correct answer can be deduced from the information in the passage. Moreover, make sure that there is little to none logical reasoning needed to answer the question.\n" +
            "- Do not require outside knowledge to answer the question correctly. All the information needed is in the passage.\n";
        
        this.outputFormat = (explanationInstructions) => "## Output Format: \n" +    
            "Provide your response in the following format: \n" +
            "\n" +
            "<passage> \n" +
            "[Insert your passage here] \n" +
            "</passage> \n" +
            "\n" +
            "<question> \n" +
            "[Insert your question here] \n" +
            "</question> \n" +
            "\n" +
            "<answer_choices> \n" +
            "A) [First answer choice] \n" +
            "B) [Second answer choice] \n" +
            "C) [Third answer choice] \n" +
            "D) [Fourth answer choice] \n" +
            "</answer_choices> \n" +
            "\n" +
            "<correct_answer> [Indicate the correct answer. Only use the letter (A, B, C, or D)] </correct_answer> \n" +
            "\n" +
            "<explanation> \n" +
            `[${explanationInstructions} In each step, use simple languages so that a 2nd grader can understand. Refers to the answer choices as (A), (B), (C), or (D). If multiple choices are incorrect for the same reason, you can group them together. Keep the explanation short and concise. Do not repeat the correct choice. Do not use colon (:).] \n` +
            "</explanation> \n" +
            "\n" +
            "Finally, do not provide in-text citations.";
    }

    // Only validation: check if there are 4 answer choices and correct answer is valid
    validateAnswerChoices(choices: string[], correctAnswerIndex: number): boolean {
        return choices.length === 4 && 
               [0, 1, 2, 3].includes(correctAnswerIndex);
    }

    // Common formatting methods
    formatName(name: string): string {
        // Format italic names with asterisks
        return name.replace(/\*([^*]+)\*/g, '<i>$1</i>');
    }

    // Common prompt generation
    generatePrompt(topicInstance: string): string {
        const choices = ["A", "B", "C", "D"];
        const correctAnswer = choices[Math.floor(Math.random() * choices.length)];

        return `Make a ${this.type} question. Follow the system intructions as well as the following:` +
            '\n' +
            (this.type === 'Paired Passage' ? '- The length of each passage is 60-70 words.\n' : '') +
            (this.evidenceType ? `- The question type is ${this.evidenceType}\n` : '') +
            this.prompts[this.difficulty] +
            `- The topic of the passage is ${this.topic}.${topicInstance ? ` Specifically, ${topicInstance}.` : ''}` + "\n" +
            `- The correct answer is ${correctAnswer}.` + "\n" +
            `- Use Grounding with Google Search.`;

            ;
    }

    // Helper method to build prompts
    buildPrompt(difficulty: DifficultyLevel, topic: string, additionalInstructions: string = ''): string {
        return commonPrompts.difficultyLevels[difficulty].length + '\n' +
               commonPrompts.difficultyLevels[difficulty].passage + '\n' +
               commonPrompts.difficultyLevels[difficulty].choices + '\n' +
               (additionalInstructions ? additionalInstructions + "\n" : "");
    }

    buildSystemInstructions(example: string, explanationInstructions: string): string {
        return example + '\n' +
            this.passageStructure + '\n' +
            this.question + '\n' +
            this.answerChoiceGuide + '\n' +
            this.overallDifficulty + '\n' +
            this.outputFormat(explanationInstructions);
    }
}

// Word in Context Question
class WordInContextQuestion extends ReadingQuestion {
    constructor(topic: string, difficulty: DifficultyLevel) {
        super('Word in Context', topic, difficulty);
        this.example = 'You are tasked with generating a Word in Context question for the digital SAT test.\n\n' +
            'Your goal is to create a high-quality Word in Context question that tests a student\'s ability to find a word that most suitably fills in the blank.\n\n' +
            '# Example\n' +
            'Rejecting the premise that the literary magazine Ebony and Topaz (1927) should present a unified vision of Black American identity, editor Charles S. Johnson fostered his contributors\' diverse perspectives by promoting their authorial autonomy. Johnson\'s self-effacement diverged from the editorial stances of W.E.B. Du Bois and Alain Locke, whose decisions for their publications were more ______.\n\n' +
            'Which choice completes the text with the most logical and precise word or phrase?\n' +
            'A. proficient\n' +
            'B. dogmatic\n' +
            'C. ambiguous\n' +
            'D. unpretentious\n\n'
                         
        this.passageStructure = this.passageStructure + '\n' +
            '- The passage is less analytical and argumentative. It is more descriptive and straightforward.\n' +
            '- The passage is sprinkled with context clues (transition words, punctuations, synonyms, paraphrased versions, contrasting words, etc) to help the student figure out the meaning of the blank.\n' +
            '- The context clues must spell out what the meaning of the blank is directly. No inference is needed.\n' +
            '- However, the correct answer must not be a word that is directly mentioned in the passage.\n' +
            '- Words that are replaced by the blank are often:\n' +
            '- Technical or Scientific Terms with Contextual Meaning (Homogeneous = Uniform, Dormant = inactive, Buttress = support)\n' +
            '- Abstract or Philosophical Terms (extraordinary = singular, Indicator = something that supports)\n' +
            '- Historical and Economic Terms (Contained = featured, Defunct = no longer existing, Fulfills = answers)\n' +
            '- To find the correct answer, students need to use the context to figure out the meaning of the blank.';

        this.question = this.question + '- The question will almost always be “Which choice completes the text with the most logical and precise word or phrase?”'

        this.answerChoiceGuide = this.answerChoiceGuide + '\n' +
            '- The answer choices are 4 words. Occasionally, the answer choices are phrases that contain prepositions.\n' +
            '- The answer choices might use an uncommon 2nd meaning of a word. For example, “chance”  sometimes means “to attempt”. This is often the correct answer. However, this is not always the case. \n' +
            '- If “a” and “an” are right before the blank, remove it from the passage and add it to the answer choices.';

        this.explanationInstructions = 'First, reason out the meaning of the word to fill in the blank. Afterwards, find an answer choice that matches this meaning. Provide alternative approaches if the student can’t figure it out (determine if the meaning is positive/negative, use root words to guess meanings).';

        this.systemInstructions = this.buildSystemInstructions(this.example, this.explanationInstructions);

        const choices = ["A", "B", "C", "D"];
        const correctAnswer = choices[Math.floor(Math.random() * choices.length)];

        this.prompts = {
            Easy: "- The passage is relatively short, around 40-60 words.\n" +
                  commonPrompts.difficultyLevels['Easy'].passage + '\n' +
                  commonPrompts.difficultyLevels['Easy'].choices + '\n' +
                  '- The passage provides a direct definition or restates the meaning of the word.\n',
            Medium: "- The passage is relatively short, around 40-60 words.\n" +
                    commonPrompts.difficultyLevels['Medium'].passage + '\n' +
                    commonPrompts.difficultyLevels['Medium'].choices + '\n',

            Hard: "- The passage is short, around 50-70 words.\n" +
                  "- The passage uses formal scientific words, similar to those in actual studies.\n" +
                  "- The passage has more context clues. However, they are harder to be understood.\n" +
                  "- The answer choices use semi-advanced vocabulary that is also uses in actual researches, articles and studies.\n" +
                  commonPrompts.difficultyLevels['Medium'].choices + '\n',
        };
    }
}

// Main Idea Question
class MainIdeaQuestion extends ReadingQuestion {
    constructor(topic: string, difficulty: DifficultyLevel) {
        super('Main Idea', topic, difficulty);

        this.example = 'You are tasked with generating a Main Idea question for the digital SAT test.\n\n' +
            'Your goal is to create a high-quality Main Idea question that tests a student\'s ability to find the main idea of a passage.\n\n' +
            '# Example\n' +
            'Believing that living in an impractical space can heighten awareness and even improve health, conceptual artists Madeline Gins and Shusaku Arakawa designed an apartment building in Japan to be more fanciful than functional. A kitchen counter is chest-high on one side and knee-high on the other; a ceiling has a door to nowhere. The effect is disorienting but invigorating: after four years there, filmmaker Nobu Yamaoka reported significant health benefits.\n\n' +
            'Which choice best states the main idea of the text?\n\n' +
            'A. Although inhabiting a home surrounded by fanciful features such as those designed by Gins and Arakawa can be rejuvenating, it is unsustainable.\n' +
            'B. Designing disorienting spaces like those in the Gins and Arakawa building is the most effective way to create a physically stimulating environment.\n' +
            'C. As a filmmaker, Yamaoka has long supported the designs of conceptual artists such as Gins and Arakawa.\n' +
            'D. Although impractical, the design of the apartment building by Gins and Arakawa may improve the well-being of the building\'s residents.\n\n'
            
        this.passageStructure = this.passageStructure + '\n' +
            '- To find the correct answer, students need to figure out what the main idea of the passage is.';

        this.question = this.question + '- The question will almost always be "Which choice best states the main idea of the text?"';

        this.answerChoiceGuide = this.answerChoiceGuide

        this.explanationInstructions = 'Extract out key information using simple languages. Then, make a prediction of how the main idea would look like. Finally, point out the correct answer choice based on the prediction. Afterwards, explain why incorrect choices are incorrect.';

        this.systemInstructions = this.buildSystemInstructions(this.example, this.explanationInstructions);

        this.prompts = {
            Easy: this.buildPrompt('Easy', topic),
            Medium: this.buildPrompt('Medium', topic),
            Hard: this.buildPrompt('Hard', topic, '- The length of answer choices are long, often using a long noun in the passage.')
        };
    }
}

// Specific Detail Question
class SpecificDetailQuestion extends ReadingQuestion {
    constructor(topic: string, difficulty: DifficultyLevel) {
        super('Specific Detail', topic, difficulty);
        this.example = 'You are tasked with generating a Specific Detail question for the digital SAT test.\n\n' +
            'Your goal is to create a high-quality Specific Detail question that tests a student\'s ability to find a specific detail mentioned in a passage.\n\n' +
            '# Example\n' +
            'Utah is home to Pando, a colony of about 47,000 quaking aspen trees that all share a single root system. Pando is one of the largest single organisms by mass on Earth, but ecologists are worried that its growth is declining in part because of grazing by animals. The ecologists say that strong fences could prevent deer from eating young trees and help Pando start thriving again.\n\n' +
            'According to the text, why are ecologists worried about Pando?\n\n' +
            'A. It isn\'t growing at the same rate it used to.\n' +
            'B. It isn\'t producing young trees anymore.\n' +
            'C. It can\'t grow into new areas because it is blocked by fences.\n' +
            'D. Its root system can\'t support many more new trees.\n\n';

        this.question = this.question + 
            '- The question asks about a specific detail in the text. It often looks something similar to these: \n' +
            '- According to the text, what is true about X? \n' +
            '- According to the text, why is X…? \n' +
            '- What does the text indicate about X? \n' +
            '- Based on the text, what can be concluded about X? \n' +
            '- What does the text most strongly suggest about X? (for this one, a small logical step is needed to find the answer)' +
            '- In most cases, the student does not need to make any logical steps: the correct answer is just a paraphrased version of a sentence or two in the passage.';

        this.explanationInstructions = 'Extract out key information using simple languages. Then, make a prediction of how the correct answer would look like. Finally, point out the correct answer choice based on the prediction. Afterwards, explain why incorrect choices are incorrect.';

        this.prompts = {
            Easy: this.buildPrompt('Easy', topic),
            Medium: this.buildPrompt('Medium', topic),
            Hard: this.buildPrompt('Hard', topic, '- The length of answer choices are long, often using a long noun in the passage.\n' +
                                          '- Incorrect choices often uses words that appeared in the passage, while the correct choice doesn\'t, making it way harder for students to find the correct answer.')
        };

        this.systemInstructions = this.buildSystemInstructions(this.example, this.explanationInstructions);
    }
}

// Main Purpose Question
class MainPurposeQuestion extends ReadingQuestion {
    constructor(topic: string, difficulty: DifficultyLevel) {
        super('Main Purpose', topic, difficulty);
        this.example = 'You are tasked with generating a Main Purpose question for the digital SAT test.\n\n' +
            'Your goal is to create a high-quality Main Purpose question that tests a student\'s ability to find the main purpose of a passage.\n\n' +
            '# Example\n' +
            'Studying late nineteenth- and early twentieth-century artifacts from an agricultural and domestic site in Texas, archaeologist Ayana O. Flewellen found that Black women employed as farm workers utilized hook-and-eye closures to fasten their clothes at the waist, giving themselves a silhouette similar to the one that was popular in contemporary fashion and typically achieved through more restrictive garments such as corsets. Flewellen argues that this sartorial practice shows that these women balanced hegemonic ideals of femininity with the requirements of their physically demanding occupation.\n\n' +
            'Which choice best states the main purpose of the text?\n\n' +
            'A. To describe an unexpected discovery that altered a researcher\'s view of how rapidly fashions among Black female farmworkers in late nineteenth- and early twentieth-century Texas changed during the period.\n' +
            'B. To discuss research that investigated the ways in which Black female farmworkers in late nineteenth- and early twentieth-century Texas used fashion practices to resist traditional gender ideals.\n' +
            'C. To evaluate a scholarly work that offers explanations for the impact of urban fashion ideals on Black female farmworkers in late nineteenth- and early twentieth-century Texas.\n' +
            'D. To summarize the findings of a study that explored factors influencing a fashion practice among Black female farmworkers in late nineteenth- and early twentieth-century Texas.\n\n'
        
        this.question = this.question +
            '- The question will almost always be "Which choice best states the main purpose of the text?"\n';

        this.passageStructure = this.passageStructure + '\n' +
            '- To find the correct answer, students need to figure out what the main purpose of the passage is.';

        this.answerChoiceGuide = this.answerChoiceGuide + '\n' +
            '- Typically, the correct choice is abstract, while other choices are more direct. This is not always the case, however.\n' +
            '- The main purpose is usually the main idea rephrased in an indirect way.\n' +
            '- Do not split the answer choice into 2 clauses. Do not add a comma clause at the end.';

        this.explanationInstructions = 'Extract out key information using simple languages so that a 2nd grader can understand. Then, point out the correct answer choice based on the information. Afterwards, explain why incorrect choices are incorrect in a simple way.';

        this.prompts = {
            Easy: this.buildPrompt('Easy', topic),
            Medium: this.buildPrompt('Medium', topic),
            Hard: this.buildPrompt('Hard', topic, '- The lengths of answer choices are moderate.\n' +
                '- Do not include 2 or more purposes in 1 answer choice.\n' +
                '- Incorrect choices often are more specific and uses words that appeared in the passage. On the other hand, the correct choice is more general, making it way harder for students to find the correct answer. This is not always the case, however.')
        };

        this.systemInstructions = this.buildSystemInstructions(this.example, this.explanationInstructions);
    }
}

class MainPurposeUnderlinedQuestion extends ReadingQuestion {
    constructor(topic: string, difficulty: DifficultyLevel) {
        super('Main Purpose Underlined', topic, difficulty);

        this.example =
            'You are tasked with generating a Main Purpose (Underlined) question for the digital SAT test.\n\n' +
            'Your goal is to create a high-quality Main Purpose (Underlined) question that tests a student\'s ability to find the main purpose of an underlined part of a passage.\n\n' +
            '# Example\n' +
            'Archeological excavation of Market Street Chinatown, a nineteenth-century Chinese American community in San Jose, California, provided the first evidence that Asian food products were imported to the United States in the 1800s: bones from a freshwater fish species native to Southeast Asia. <u>Jinshanzhuang—Hong Kong–based import/export firms—likely coordinated the fish’s transport from Chinese-operated fisheries in Vietnam and Malaysia to North American markets</u>. This route reveals the (often overlooked) multinational dimensions of the trade networks linking Chinese diaspora communities. \n\n' +
            'Which choice best describes the function of the underlined sentence in the text as a whole?\n\n' +
            'A. It explains why efforts to determine the country of origin of the items mentioned in the previous sentence remain inconclusive. \n' +
            'B. It provides information that helps support a claim about a discovery’s significance that is presented in the following sentence. \n' +
            'C. It traces the steps that were taken to locate and recover the objects that are described in the previous sentence. \n' +
            'D. It outlines a hypothesis that additional evidence discussed in the following sentence casts some doubt on. \n\n';
            
        this.passageStructure = this.passageStructure + '\n' +
            '- The underlined sentence is surrounded by the HTML tag <u></u>\n' +
            '- To find the correct answer, students need to figure out what the main purpose of the underlined sentence is.';

        this.question = this.question + '- The question will almost always be "Which choice best describes the function of the underlined sentence in the text as a whole?"';

        this.answerChoiceGuide = this.answerChoiceGuide + '\n' +
            '- Typically, the correct choice is abstract, while other choices are more direct. This is not always the case, however.\n' +
            '- The main purpose is usually the main idea rephrased in an indirect way.\n' +
            '- Do not split the answer choice into 2 clauses. Do not add a comma clause at the end.';

        this.explanationInstructions = 'Extract out key information using simple languages. Then, make a prediction of how the correct answer would look like. Finally, point out the correct answer choice based on the prediction. Afterwards, explain why incorrect choices are incorrect.';
   
        this.systemInstructions = this.buildSystemInstructions(this.example, this.explanationInstructions);

        this.prompts = {
            Easy: this.buildPrompt('Easy', topic),
            Medium: this.buildPrompt('Medium', topic),
            Hard: this.buildPrompt('Hard', topic, '- The lengths of answer choices are moderate.\n' +
                '- Do not include 2 or more purposes in 1 answer choice.\n' +
                '- Incorrect choices often are more specific and uses words that appeared in the passage. On the other hand, the correct choice is more general, making it way harder for students to find the correct answer. This is not always the case, however.')
        };
    }
}

// Overall Structure Question
class OverallStructureQuestion extends ReadingQuestion {
    constructor(topic: string, difficulty: DifficultyLevel) {
        super('Overall Structure', topic, difficulty);
        this.example = 'You are tasked with generating an Overall Structure question for the digital SAT test.\n\n' +
            'Your goal is to create a high-quality Overall Structure question that tests a student\'s ability to identify how a passage is organized.\n\n' +
            '# Example\n' +
            'Using NASA\'s powerful James Webb Space Telescope (JWST), Mercedes López-Morales and colleagues measured the wavelengths of light traveling through the atmosphere of WASP-39b, an exoplanet, or planet outside our solar system. Different molecules absorb different wavelengths of light, and the wavelength measurements showed the presence of carbon dioxide (CO₂) in WASP-39b\'s atmosphere. This finding not only offers the first decisive evidence of CO₂ in the atmosphere of an exoplanet but also illustrates the potential for future scientific breakthroughs held by the JWST.\n\n' +
            'Which choice best describes the overall structure of the text?\n\n' +
            'A. It discusses a method used by some researchers, then states why an alternative method is superior to it.\n' +
            'B. It describes how researchers made a scientific discovery, then explains the importance of that discovery.\n' +
            'C. It outlines the steps taken in a scientific study, then presents a hypothesis based on that study.\n' +
            'D. It examines how a group of scientists reached a conclusion, then shows how other scientists have challenged that conclusion.\n\n'

        this.question = this.question + '- The question will always be "Which choice best describes the overall structure of the text?"';

        this.answerChoiceGuide = this.answerChoiceGuide + '\n' +
            '- The 4 answer choices are fairly different from each other: they are basically 4 entirely different structures with small overlaps every now and then.';

        this.explanationInstructions = 'Extract out key information using simple languages. Then, make a prediction of how the correct answer would look like. Finally, point out the correct answer choice based on the prediction. Afterwards, explain why incorrect choices are incorrect.';

        this.systemInstructions = this.buildSystemInstructions(this.example, this.explanationInstructions);

        this.prompts = {
            Easy: this.buildPrompt('Easy', topic),
            Medium: this.buildPrompt('Medium', topic),
            Hard: this.buildPrompt('Hard', topic, '- The length of answer choices are moderately long. One way to do this is to make them consists of 3 statements.')
        };
    }
}

// Inference Question
class InferenceQuestion extends ReadingQuestion {
    constructor(topic: string, difficulty: DifficultyLevel) {
        super('Inference', topic, difficulty);
        this.example = 'You are tasked with generating an Inference question for the digital SAT test.\n\n' +
            'Your goal is to create a high-quality Inference question that tests a student\'s ability to draw logical conclusions from the information presented in the passage.\n\n' +
            '# Example\n' +
            'Marta Coll and colleagues\' 2010 Mediterranean Sea biodiversity census reported approximately 17,000 species, nearly double the number reported in Carlo Bianchi and Carla Morri\'s 2000 census—a difference only partly attributable to the description of new invertebrate species in the interim. Another factor is that the morphological variability of microorganisms is poorly understood compared to that of vertebrates, invertebrates, plants, and algae, creating uncertainty about how to evaluate microorganisms as species. Researchers\' decisions on such matters therefore can be highly consequential. Indeed, the two censuses reported similar counts of vertebrate, plant, and algal species, suggesting that ______\n\n' +
            'Which choice most logically completes the text?\n\n' +
            'A. Coll and colleagues reported a much higher number of species than Bianchi and Morri did largely due to the inclusion of invertebrate species that had not been described at the time of Bianchi and Morri\'s census.\n' +
            'B. some differences observed in microorganisms may have been treated as variations within species by Bianchi and Morri but treated as indicative of distinct species by Coll and colleagues.\n' +
            'C. Bianchi and Morri may have been less sensitive to the degree of morphological variation displayed within a typical species of microorganism than Coll and colleagues were.\n' +
            'D. the absence of clarity regarding how to differentiate among species of microorganisms may have resulted in Coll and colleagues underestimating the number of microorganism species.\n\n';

        this.passageStructure = this.passageStructure + '\n' +
            '- The passage ends with a blank, and students need to find the answer that logically completes the blank. The ending is typically “. This suggests that ______” or “, suggesting that ______”.\n' +
            '- The passage presents facts, observations, or a discussion, but it does not explicitly state the conclusion.\n' +
            '- To find the correct answer, students need to draw conclusions based on indirect evidence rather than explicit statements in the text.\n' +
            '- The conclusion must be something specific and not too general.';

        this.question = this.question + '- The question will almost always be "Which choice most logically completes the text?"';

        this.overallDifficulty = '- The question and the passage should be challenging but fair, suitable for high school students taking the SAT.\n' +
            '- Ensure that the correct answer can be logically deduced from the information in the passage. Moreover, make sure that only at most 2 logical steps are needed to arrive at the answer, and each logical step is small/moderate.\n' +
            '- Do not require outside knowledge to answer the question correctly. All the information needed is in the passage.\n' +
            '- The question should not be directly answered in the text but should be supported by evidence from the passage.';

        this.explanationInstructions = 'Summarize the passage and extract out key information using simple languages so that a 2nd grader can understand. Then, make an inference from the information and select the correct answer choice. Afterwards, explain why incorrect choices are incorrect in a simple way so that a 2nd grader can understand.';

        this.systemInstructions = this.buildSystemInstructions(this.example, this.explanationInstructions);

        this.prompts = {
            Easy: this.buildPrompt('Easy', topic),
            Medium: this.buildPrompt('Medium', topic),
            Hard: this.buildPrompt('Hard', topic, '- To arrive at the correct answer, the students need to synthesize key information and perform multiple tricky logical steps.\n' +
                                          '- The length of answer choices are long, often using a long noun in the passage.')
        };
    }
}

// Command of Evidence Question
class CommandOfEvidenceQuestion extends ReadingQuestion {
    constructor(topic: string, difficulty: DifficultyLevel, evidenceType: string) {
        super('Command of Evidence', topic, difficulty);
        this.evidenceType = evidenceType; // 'support' or 'undermine'
        this.example = 'You are tasked with generating a Command of Evidence question for the digital SAT test.\n\n' +
                         'Your goal is to create a high-quality Command of Evidence question that tests a student\'s ability to draw logical conclusions from the information presented in the passage.\n\n' +
                         '# Example\n' +
                         'Roasted green chiles are a popular ingredient in Southwestern cuisine, but the traditional roasting method of burning propane is not environmentally friendly. To see if solar power could provide a better alternative, engineer Kenneth Armijo and his team roasted batches of green chiles using between 38 and 42 heliostats, which are devices that concentrate sunlight. The team was successful in reaching the same roasting temperature used in traditional propane roasting, but they found that propane yielded faster results. While the fastest solar-roasted green chiles took six minutes, batches using propane took only four. Armijo hypothesizes that they can reduce the roasting time for solar-roasted green chiles by using more heliostats.\n\n' +
                         'Which finding, if true, would most directly support Armijo\'s hypothesis?\n\n' +
                         'A. The temperature inside the roasting drum is distributed more evenly when roasting green chiles with solar power than with propane.\n' +
                         'B. Attempts to roast green chiles using 50 heliostats yields results in fewer than six minutes.\n' +
                         'C. Green chile connoisseurs prefer the flavor of solar-roasted green chiles over the flavor of propane-roasted green chiles.\n' +
                         'D. The skins of solar-roasted green chiles are easier to peel than the skins of propane-roasted green chiles.\n\n';

        this.passageStructure = this.passageStructure + '\n' +
            '- The passage ends with a conclusion. Typically, the final sentence goes something like “The researchers conclude that…” or “[name of researcher] hypothesizes that…”.\n' +
            '- Students need to find the answer choice that logically supports or undermines this conclusion. This will be further clarified by the user.\n' +
            '- The conclusion must be something specific and not too general.';

        this.question = this.question + '- The question mirrors the conclusion of the passage. For example, if the passage said “The researchers conclude that…”, the question will be “Which finding, if true, would most directly support/undermine the researchers’ conclusion?”';

        this.overallDifficulty = '- The question and the passage should be challenging but fair, suitable for high school students taking the SAT.\n' +
            '- Ensure that the correct answer can be logically deduced from the information in the passage. Moreover, make sure that only at most 2 logical steps are needed to arrive at the answer, and each logical step is small/moderate.\n' +
            '- Do not require outside knowledge to answer the question correctly. All the information needed is in the passage.\n' +
            '- The question should not be directly answered in the text but should be supported by evidence from the passage.';

        this.explanationInstructions = 'Summarize the passage and extract out key information using simple languages so that a 2nd grader can understand. Then, make an inference from the information to see what kind of finding would support/undermine the passage and select the correct answer choice. Afterwards, explain why incorrect choices are incorrect in a simple way.';

        this.systemInstructions = this.buildSystemInstructions(this.example, this.explanationInstructions);

        this.prompts = {
            Easy: this.buildPrompt('Easy', topic),
            Medium: this.buildPrompt('Medium', topic),
            Hard: this.buildPrompt('Hard', topic, '- To arrive at the correct answer, the students need to synthesize key information and perform multiple tricky logical steps.\n' +
                                          '- The length of answer choices are long, often using a long noun in the passage.')
        };
    }
}

// Paired Passage Question
class PairedPassageQuestion extends ReadingQuestion {
    constructor(topic: string, difficulty: DifficultyLevel) {
        super('Paired Passage', topic, difficulty);
        this.example = 'You are tasked with generating a Pair Passage question for the digital SAT test.\n\n' +
                         'Your goal is to create a high-quality Pair Passage question that tests a student\'s ability to compare two related passages and analyze their similarities, differences, or perspectives.\n\n' +
                         '# Example\n' +
                         'In 1916, H. Dugdale Sykes disputed claims that The Two Noble Kinsmen was coauthored by William Shakespeare and John Fletcher. Sykes felt Fletcher\'s contributions to the play were obvious—Fletcher had a distinct style in his other plays, so much so that lines with that style were considered sufficient evidence of Fletcher\'s authorship. But for the lines not deemed to be by Fletcher, Sykes felt that their depiction of women indicated that their author was not Shakespeare but Philip Massinger.\n\n' +
                         'Scholars have accepted The Two Noble Kinsmen as coauthored by Shakespeare since the 1970s: it appears in all major one-volume editions of Shakespeare\'s complete works. Though scholars disagree about who wrote what exactly, it is generally held that on the basis of style, Shakespeare wrote all of the first act and most of the last, while John Fletcher authored most of the three middle acts.\n\n' +
                         'Based on the texts, both Sykes in Text 1 and the scholars in Text 2 would most likely agree with which statement?\n\n' +
                         'A. John Fletcher\'s writing has a unique, readily identifiable style.\n' +
                         'B. The women characters in John Fletcher\'s plays are similar to the women characters in Philip Massinger\'s plays.\n' +
                         'C. The Two Noble Kinsmen belongs in one-volume compilations of Shakespeare\'s complete plays.\n' +
                         'D. Philip Massinger\'s style in the first and last acts of The Two Noble Kinsmen is an homage to Shakespeare\'s style.\n\n';

        this.passageStructure = '- Except for human names, all other names (e.g. scientific species names, novel names, poem names, exhibition names, song names, architecture names) are italic. This should be marked by an asterisk (*) at the start and end of the name. Concepts that have normal English names are not italic.\n' +
            '- The passage topic is one of Social Science, Natural Science, or Humanities.\n' +
            '- Examples include Scientific Research & Studies, Social Science, Economic Analysis, Environmental Science, Human Behavior & Psychology, Literature, and Art Interpretation.\n' +
            '- The passage refers to actual real studies or scientific articles. Therefore, it also includes the actual researcher’s name. Usually, it also includes the actual published year. Do not make these up. Do not state where the study was published.\n' +
            '- Some common passage structures include: \n' +
            '- Comparison of Two Works or Perspectives: One passage presents a claim, and the other presents a counterclaim or elaborates on the same idea differently. \n' +
            '- Scientific or Historical Debate: One passage introduces a scientific or historical finding, while the second passage critiques, supports, or adds context to the claim. \n' +
            '- Cause-and-Effect Relationship Between the Two Passages: One passage describes an event or phenomenon, and the second passage discusses its consequences or broader implications.\n' +
            '- Literary or Philosophical Interpretation: One passage presents a literary or philosophical concept, and the second passage extends or critiques it.\n' +
            '- Students need to find the answer choice that correctly identifies the viewpoint of both texts. \n' +
            '- The viewpoint must be something specific and not too general.';

        this.question += '- The question will ask about the relationship between the 2 passages. Some examples are: \n' +
            '- Based on the texts, both Text 1 and Text 2 would most likely agree with which statement? \n' +
            '- Which choice best describes a difference in how the author of Text 1 and the author of Text 2 view X? \n' +
            '- Based on the texts, how would X (Text 2) most likely respond to Y discussed in Text 1? \n' +
            '- Based on the texts, if X (Text 1) and Y (Text 2) were aware of Z, they would most likely agree with which statement? \n' +
            '- Based on the texts, how would the author of Text 2 most likely respond to the underlined claim in Text 1? (Add <u>...</u> around the underlined claim). \n' +
            '- The question will ask about the relationship between the 2 passages. Some examples are: \n' +
            '- Based on the texts, both Text 1 and Text 2 would most likely agree with which statement? \n' +
            '- Which choice best describes a difference in how the author of Text 1 and the author of Text 2 view X? \n' +
            '- Based on the texts, how would X (Text 2) most likely respond to Y discussed in Text 1?';

        this.answerChoiceGuide = this.answerChoiceGuide + '\n' +
            '- The answer choices must be something specific and not too general.';

        this.overallDifficulty += '- The answer choices are closely related to the passage.';

        this.outputFormat = (explanationInstructions) => {
            return "## Output Format:\n" +  
                "Provide your response in the following format:\n" +
                "\n" +
                "<passage>\n" +
                "[Insert your passage here]\n" +
                "</passage>\n\n" +
                "<passage_2>\n" +
                "[Insert your passage 2 here]\n" +
                "</passage_2>\n\n" +
                "<question>\n" +
                "[Insert your question here]\n" +
                "</question>\n\n" +
                "<answer_choices>\n" +
                "A) [First answer choice]\n" +
                "B) [Second answer choice]\n" +
                "C) [Third answer choice]\n" +
                "D) [Fourth answer choice]\n" +
                "</answer_choices>\n\n" +
                "<correct_answer>\n" +
                "[Indicate the correct answer. Only use the letter (A, B, C, or D)]\n" +
                "</correct_answer>\n\n" +     
                "<explanation>\n" +
                "[Use simple languages so that a 2nd grader can understand. First, determine the opinion of text 1. Do the same for text 2. Then, compare the 2 opinions and determine the relationship of both texts. Determine the correct choice based on this. Afterwards, explain why incorrect choices are incorrect in a simple way so that a 2nd grader can understand. Refers to the answer choices as (A), (B), (C), or (D). If multiple choices are incorrect for the same reason, you can group them together. Keep the explanation short and concise. Do not repeat the correct choice. Do not use colon (:).] \n" +
                "</explanation>\n";
        }

        this.explanationInstructions = 'Use simple languages so that a 2nd grader can understand. First, determine the opinion of text 1. Do the same for text 2. Then, compare the 2 opinions and determine the relationship of both texts. Determine the correct choice based on this. Afterwards, explain why incorrect choices are incorrect in a simple way so that a 2nd grader can understand.';

        const choices = ["A", "B", "C", "D"];
        const correctAnswer = choices[Math.floor(Math.random() * choices.length)];

        this.prompts = {
            Easy: commonPrompts.difficultyLevels['Easy'].passage + '\n' +
                  commonPrompts.difficultyLevels['Easy'].choices + '\n',
            Medium: commonPrompts.difficultyLevels['Medium'].passage + '\n' +
                    commonPrompts.difficultyLevels['Medium'].choices + '\n',
            Hard: commonPrompts.difficultyLevels['Hard'].passage + '\n' +
                  commonPrompts.difficultyLevels['Medium'].choices + '\n',
        };

        this.systemInstructions = this.buildSystemInstructions(this.example, this.explanationInstructions);
    }
}

// Writing Question Classes

// Student's Notes Question (W1)
class StudentsNotesQuestion extends ReadingQuestion {
    constructor(topic: string, difficulty: DifficultyLevel) {
        super("Student's Notes", topic, difficulty);

        this.example = 'You are tasked with generating a Student\'s Notes question for the digital SAT test.\n\n' +
            'Your goal is to create a high-quality Student\'s Notes question that tests a student\'s ability to synthesize information to achieve a certain communication goal.\n\n' +
            '# Example\n' +
            'Some powerful works of literature have so influenced readers that new legislation has been passed as a result.\n' +
            'The Interesting Narrative of the Life of Olaudah Equiano (1789) is the autobiography of a man who endured slavery on both sides of the Atlantic.\n' +
            'Equiano\'s book contributed to the passage of the Slave Trade Act of 1807.\n' +
            'The Jungle (1906) is a fictional work by Upton Sinclair that describes unsanitary conditions in US meatpacking plants.\n' +
            'Sinclair\'s book contributed to the passage of the Pure Food and Drug Act in 1906.\n\n' +
            'The student wants to emphasize a difference between the two books. Which choice most effectively uses relevant information from the notes to accomplish this goal?\n' +
            'A. Although both are powerful works of literature that contributed to new legislation, Equiano\'s book is an autobiography, while Sinclair\'s is fictional.\n' +
            'B. They may have written about different topics, but Equiano and Sinclair both influenced readers.\n' +
            'C. The 1807 Slave Trade Act resulted in part from a book by Equiano, while the 1906 Pure Food and Drug Act resulted in part from a book by Sinclair.\n' +
            'D. The Interesting Narrative of the Life of Olaudah Equiano and The Jungle are two works of literature that contributed to new legislation (concerning the slave trade and food safety, respectively).\n\n';

        this.passageStructure = '# Instructions\n' +
            'Follow these guidelines:\n' +
            '## Notes Structure:\n' +
            '- Present as bullet points containing research information. Each bullet point is a sentence. Separate the bullet points from each other with " || ".\n' +
            '- Except for human names, all other names (e.g. scientific species names, novel names, poem names, exhibition names, song names, architecture names) are italic. This should be marked by an asterisk (*) at the start and end of the name.\n' +
            '- The note topic is one of Social Science, Natural Science, or Humanities.\n' +
            '- Examples include Scientific Research & Studies, Social Science, Economic Analysis, Environmental Science, Human Behavior & Psychology, Literature, and Art Interpretation.\n' +
            '- The note refers to actual real studies. Therefore, it should also include the actual researcher\'s name. The actual publish year is sometimes included. Do not make these up. Do not state where the study was published.\n' +
            '- The bullet points typically follow this format:\n' +
            '  1st bullet point: Background about the topic\n' +
            '  2nd: Particular facts, dates, locations, or characteristics\n' +
            '  3rd: Supporting information or examples\n' +
            '  4th: More specific details or examples\n' +
            '  5th: Concluding information or specific examples\n' +
            '- Some common note structures include:\n' +
            '  - Problem-Solution Structure (Example: recipients couldn\'t pay postage → postage stamps)\n' +
            '  - General-to-Specific Pattern (Example: clay selection factors → earthenware)\n' +
            '  - Compare-Contrast Structure (Example: slow blinks vs. quick blinks effects)\n' +
            '  - Chronological/Sequential Structure (Example: Step-by-step turtle egg-laying process)\n' +
            '  - Cause-Effect Structure (Example: Flying buttresses invention → Architectural possibilities)';

        this.question = '## Question:\n' +
            '- All questions follow the pattern: "The student wants to [specific goal]. Which choice most effectively uses relevant information from the notes to accomplish this goal?"\n' +
            '- The specific goal can be one of these categories:\n' +
            '  - Descriptive Questions (Example: "describe how fabric is used", "describe the wok\'s shape")\n' +
            '  - Similarity/Difference Questions (Example: "emphasize a similarity between the two works", "contrast the purposes of the two maps")\n' +
            '  - Introductory Questions (Example: "introduce an artist", "introduce the primary aim")\n' +
            '  - Explanatory Questions (Example: "explain an advantage of the new platinum catalyst", "explain the origin of the species\' name")\n' +
            '  - Audience-Specific Questions, also known as familiar/unfamiliar questions (Example: "present the study to an audience already familiar with environmental DNA", "introduce Kahlo to an audience unfamiliar with the artist")';

        this.answerChoiceGuide = '## Answer Choices:\n' +
            '- Generate four answer choices (A, B, C, and D).\n' +
            '- Each choice should be a complete sentence that could accomplish the stated goal.\n' +
            '- One answer should be correct and directly address the communication goal using relevant information.\n' +
            '- Three answers should be incorrect for various reasons:\n' +
            '  - Addresses wrong goal or audience\n' +
            '  - Uses irrelevant information\n' +
            '  - Fails to synthesize information effectively\n' +
            '  - Misses the key point of the communication goal\n' +
            '- Usually, 2 incorrect choices are easier to eliminate, while the last incorrect choice is harder to distinguish. This can be done by having 2 answer choices of the same transition type and requiring the user to make fine distinctions between them. This is more for more advanced questions, however.';

        this.overallDifficulty = '## Overall Difficulty:\n' +
            '- The question and the passage should be challenging but fair, suitable for high school students taking the SAT.\n' +
            '- Ensure that the correct answer can be logically deduced from the information in the passage.\n' +
            '- Do not require outside knowledge to answer the question correctly. All the information needed is in the passage.';

        this.outputFormat = (explanationInstructions) => '## Output Format:\n' +
            'Provide your response in the following format:\n\n' +
            '<passage>\n' +
            '[Insert your notes here as a list of sentences separated by " || ". Do not add asterisk or "-" before the bullet points.]\n' +
            '</passage>\n\n' +
            '<question>\n' +
            '[Insert your question here]\n' +
            '</question>\n\n' +
            '<answer_choices>\n' +
            'A) [First answer choice]\n' +
            'B) [Second answer choice]\n' +
            'C) [Third answer choice]\n' +
            'D) [Fourth answer choice]\n' +
            '</answer_choices>\n' +
            '<correct_answer> [Indicate the correct answer (A, B, C, or D)] </correct_answer>\n' +
            '<explanation>\n' +
            '[Follow this exact pattern:\n' +
            'Here, the keyword in the question is "[list out keywords. Make sure that they are short words and not a huge phrase]", as the student wants to <...>.\n' +
            'Looking at the answers:\n' +
            'explain what each answer choices achieved\n' +
            'Hence, (<...>) is the correct answer.\n' +
            ']\n' +
            '</explanation>\n\n' +
            'Finally, do not provide in-text citations.';

        // This line is useless
        this.explanationInstructions = 'Follow this exact pattern: Here, the keyword in the question is "<...>", as the student wants to <...>. Looking at the answers: explain what each answer choices achieved Hence, (<...>) is the correct answer.';

        this.systemInstructions = this.buildSystemInstructions(this.example, this.explanationInstructions);

        this.prompts = {
            Easy: '- The note contains 4-5 bullet points. Each bullet points are 8-12 words.\n' +
                  '- The passage uses simple to moderate words that are understandable by a highschooler.\n' +
                  '- The incorrect answer choices are not too tricky to eliminate.\n' +
                  '- The topic are familiar and concrete.\n' +
                  '- The question is direct and single-focus.\n',
            Medium: '- The note contains 5-6 bullet points. Each bullet points are 12-18 words.\n' +
                    '- The passage uses scientific words that require senior-highschool to first-year-university reading level to comprehend.\n' +
                    '- The sentences are complex with multiple clauses\n' +
                    '- The incorrect answer choices are somewhat tricky to eliminate, requiring the students to read carefully.\n' +
                    '- The topic and concepts are more abstract ideas, requiring synthesis.\n' +
                    '- The question is multi-layered, often requiring audience awareness (familiar/unfamiliar).\n', 
            Hard: '- The note contains 5-6 bullet points. Each bullet points are 15-25 words.\n' +
                  '- The passage uses scientific words that require senior-highschool to first-year-university reading level to comprehend.\n' +
                  '- The sentences contain multiple embedded clauses, sophisticated syntax\n' +
                  '- The incorrect answer choices are somewhat tricky to eliminate, requiring the students to read carefully.\n' +
                  '- The topic and concepts are abstract theories, complex scientific processes, with nuanced relationships.\n' +
                  '- The question is sophisticated synthesis requiring deep understanding.\n',
        };
    }
}

// Transitions Question (W1)
class TransitionsQuestion extends ReadingQuestion {
    constructor(topic: string, difficulty: DifficultyLevel) {
        super('Transitions', topic, difficulty);

        this.example = 'You are tasked with generating a Transitions question for the digital SAT test.\n\n' +
            'Your goal is to create a high-quality Transitions question that tests a student\'s ability to understand the relationship between sentences and choose an appropriate transition for the blank.\n\n' +
            '# Example\n' +
            'In 1815, while in exile in Jamaica, Venezuelan revolutionary Simón Bolívar penned a letter praising England\'s republican government and expressing hope that Latin American nations seeking independence from Spain might achieve something similar. The letter was addressed to a local merchant, Henry Cullen; ______ though, Bolívar\'s goal was to persuade political leaders from England and Europe to support his cause.\n\n' +
            'Which choice completes the text with the most logical transition?\n' +
            'A. additionally,\n' +
            'B. ultimately,\n' +
            'C. accordingly,\n' +
            'D. consequently\n\n';

        this.passageStructure = '# Instructions\n' +
            'Follow these guidelines:\n' +
            '## Passage Structure:\n' +
            '- Except for human names, all other names (e.g. scientific species names, novel names, poem names, exhibition names, song names, architecture names) are italic. This should be marked by an asterisk (*) at the start and end of the name.\n' +
            '- The passage topic is one of Social Science, Natural Science, or Humanities.\n' +
            '- Examples include Scientific Research & Studies, Social Science, Economic Analysis, Environmental Science, Human Behavior & Psychology, Literature, and Art Interpretation.\n' +
            '- The passage refers to actual real studies. Therefore, it should also include the actual researcher\'s name. The actual publish year is sometimes included. Do not make these up. Do not state where the study was published.\n' +
            '- Most passages follow a 2-sentence structure that present an initial statement followed by a contrasting or supporting detail. However, more sentences is also acceptable\n' +
            '- Some common passage structures include:\n' +
            '  - Problem-Solution Structure (Example: recipients couldn\'t pay postage → postage stamps)\n' +
            '  - General-to-Specific Pattern (Example: clay selection factors → earthenware)\n' +
            '  - Compare-Contrast Structure (Example: slow blinks vs. quick blinks effects)\n' +
            '  - Chronological/Sequential Structure (Example: Step-by-step turtle egg-laying process)\n' +
            '  - Cause-Effect Structure (Example: Flying buttresses invention → Architectural possibilities)\n' +
            '- Students need to find the transition that logically connects the 2 sentences.';

        this.question = '## Question:\n' +
            '- The question will always be: "Which choice completes the text with the most logical transition?"';

        this.answerChoiceGuide = '## Answer Choices:\n' +
            '- Generate four answer choices (A, B, C, and D).\n' +
            '- Each answer choice is a transition word.\n' +
            '- Type of transitions are:\n' +
            '  - Additive ("then," "next," "for example," "in fact", "In addition")\n' +
            '  - Contrast ("however," "by contrast," "nevertheless")\n' +
            '  - Cause-effect structures ("as a result," "consequently," "for this reason")\n' +
            '- One answer should be correct and connect the sentences logically.\n' +
            '- The other three should be incorrect.\n' +
            '- Usually, 2 incorrect choices are easier to eliminate, while the last incorrect choice is harder to distinguish. This can be done by having 2 answer choices of the same transition type and requiring the user to make fine distinctions between them. This is more for more advanced questions, however.';

        this.overallDifficulty = '## Overall Difficulty:\n' +
            '- The question and the passage should be challenging but fair, suitable for high school students taking the SAT.\n' +
            '- Ensure that the correct answer can be logically deduced from the information in the passage.\n' +
            '- Do not require outside knowledge to answer the question correctly. All the information needed is in the passage.';

        this.outputFormat = (explanationInstructions) => '## Output Format:\n' +
            'Provide your response in the following format:\n\n' +
            '<passage>\n' +
            '[Insert your passage here]\n' +
            '</passage>\n\n' +
            '<question>\n' +
            '[Insert your question here]\n' +
            '</question>\n\n' +
            '<answer_choices>\n' +
            'A) [First answer choice]\n' +
            'B) [Second answer choice]\n' +
            'C) [Third answer choice]\n' +
            'D) [Fourth answer choice]\n' +
            '</answer_choices>\n' +
            '<correct_answer> [Indicate the correct answer (A, B, C, or D)] </correct_answer>\n' +
            '<explanation>\n' +
            '[Follow this exact pattern:\n' +
            '"As always, remember to identify the clauses around the blank":\n' +
            '- "<clause 1>"\n' +
            '- "<clause 2>"\n' +
            'Then, explain the relationship between the 2 clauses. Choose the correct answer from there.\n' +
            'Explain briefly why other choices are wrong. Keep it short. If an answer choice is of the same transition type as the correct choice, explain why it is wrong in more details. Separate each answer choices into a new line with space in the middle.]\n' +
            '</explanation>\n\n' +
            'Finally, do not provide in-text citations.';

        this.explanationInstructions = 'Follow this exact pattern: "As always, remember to identify the clauses around the blank": - "<clause 1>" - "<clause 2>" Then, explain the relationship between the 2 clauses. Choose the correct answer from there. Explain briefly why other choices are wrong. Keep it short. If an answer choice is of the same transition type as the correct choice, explain why it is wrong in more details. Separate each answer choices into a new line with space in the middle.';

        this.systemInstructions = this.buildSystemInstructions(this.example, this.explanationInstructions);

        this.prompts = {
            Easy: '- The passage is relatively short, around 30-50 words.\n' +
                  '- The passage uses simple to moderate words that are understandable by a highschooler.\n' +
                  '- The incorrect answer choices are not too tricky to eliminate.\n',
            Medium: '- The passage is short, around 40-60 words.\n' +
                    '- The passage uses scientific words that require senior-highschool to first-year-university reading level to comprehend.\n' +
                    '- The incorrect answer choices are somewhat tricky to eliminate, requiring the students to read carefully.\n',
            Hard: '- The passage is moderate, around 50-65 words.\n' +
                  '- The passage uses formal scientific words, similar to those in actual studies.\n' +
                  '- The passage require understanding of complex abstract concepts, not just concrete facts.\n' +
                  '- The passage uses scientific names (latin names) for species and extremely long nouns, if applicable.\n' +
                  '- The passage contains more complex sentence structures.\n' +
                  '- The incorrect answer choices are tricky to eliminate, requiring the students to read extremely carefully to not get tricked.\n',        };
    }
}

export {
    ReadingQuestion as Question,
    WordInContextQuestion,
    MainIdeaQuestion,
    SpecificDetailQuestion,
    MainPurposeQuestion,
    MainPurposeUnderlinedQuestion,
    InferenceQuestion,
    CommandOfEvidenceQuestion,
    PairedPassageQuestion,
    OverallStructureQuestion,
    StudentsNotesQuestion,
    TransitionsQuestion,
};