#!/usr/bin/env node

import { QuestionObject } from '../types.ts';

/**
 * Question object interface based on the return type from createQuestion function
 */
export { QuestionObject };

/**
 * Validates a question object according to the specified requirements.
 *
 * @param question - The question object to validate
 * @returns {boolean} True if the question is valid, false otherwise
 */
export function isQuestionValid(question: QuestionObject): boolean {
    try {
        // Check if question object exists
        if (!question || typeof question !== 'object') {
            return false;
        }

        // Validate required properties exist
        const requiredProperties = ['passage', 'choices', 'correctAnswer', 'explanation', 'difficulty'];
        for (const prop of requiredProperties) {
            if (!(prop in question)) {
                return false;
            }
        }

        // Validate difficulty property
        if (!['E', 'M', 'H'].includes(question.difficulty)) {
            return false;
        }

        // Check for duplicate choices
        const choiceSet = new Set(question.choices.map(c => c.trim()));
        if (choiceSet.size !== question.choices.length) {
            return false;
        }

        // Validate choices property
        if (!validateChoices(question.choices)) {
            return false;
        }

        // Validate correctAnswer property
        if (!validateCorrectAnswer(question.correctAnswer)) {
            return false;
        }

        // Validate passage property
        if (!validatePassage(question.passage)) {
            return false;
        }

        // Validate explanation property
        if (!validateExplanation(question.explanation)) {
            return false;
        }

        // Validate that correctAnswer index corresponds to a valid choice
        if (!validateCorrectAnswerIndex(question.choices, question.correctAnswer)) {
            return false;
        }

        return true;
    } catch (error) {
        return false;
    }
}

/**
 * Validates the choices array
 */
function validateChoices(choices: any): boolean {
    // Check if choices is an array
    if (!Array.isArray(choices)) {
        return false;
    }

    // Check if choices has exactly 4 elements
    if (choices.length !== 4) {
        return false;
    }

    // Check if each choice is a non-empty string
    for (let i = 0; i < choices.length; i++) {
        const choice = choices[i];

        if (typeof choice !== 'string') {
            return false;
        }

        if (choice.trim().length === 0) {
            return false;
        }
    }

    return true;
}

/**
 * Validates the correctAnswer property
 */
function validateCorrectAnswer(correctAnswer: any): boolean {
    // Check if correctAnswer is a number
    if (typeof correctAnswer !== 'number') {
        return false;
    }

    // Check if correctAnswer is an integer
    if (!Number.isInteger(correctAnswer)) {
        return false;
    }

    // Check if correctAnswer is in valid range (0-3)
    if (correctAnswer < 0 || correctAnswer > 3) {
        return false;
    }

    return true;
}

/**
 * Validates the passage property
 */
function validatePassage(passage: any): boolean {
    // Check if passage is a string
    if (typeof passage !== 'string') {
        return false;
    }

    // Check if passage is non-empty
    if (passage.trim().length === 0) {
        return false;
    }

    // Count words in passage (split by whitespace and filter out empty strings)
    const words = passage.trim().split(/\s+/).filter(word => word.length > 0);
    const wordCount = words.length;

    // Check word count is between 10 and 80 words
    if (wordCount < 10) {
        return false;
    }

    if (wordCount > 80) {
        return false;
    }

    return true;
}

/**
 * Validates the explanation property
 */
function validateExplanation(explanation: any): boolean {
    // Check if explanation is a string
    if (typeof explanation !== 'string') {
        return false;
    }

    // Check if explanation is non-empty
    if (explanation.trim().length === 0) {
        return false;
    }

    return true;
}

/**
 * Validates that the correctAnswer index corresponds to a valid choice
 */
function validateCorrectAnswerIndex(choices: string[], correctAnswer: number): boolean {
    // This validation assumes choices and correctAnswer have already been validated individually
    if (correctAnswer >= choices.length) {
        return false;
    }

    // Additional check to ensure the choice at the correct answer index is valid
    const correctChoice = choices[correctAnswer];
    if (!correctChoice || correctChoice.trim().length === 0) {
        return false;
    }

    return true;
}
