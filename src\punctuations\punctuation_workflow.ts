import { genAI } from '../ai';
import { processPassageAndChoices } from './punctuations_blank_processor.ts';
import { isQuestionValid } from './punctuations_validator.ts';
import { QuestionData } from '../types.ts';
import fs from 'fs';

export { QuestionData };

export async function createQuestion(data: QuestionData) {
    const { originalPassage, originalChoices, originalCorrectAnswerContent } = data;

    // Format originalPassage based on originalCorrectAnswer
    const formattedPassage = originalPassage.replace(/__+/, '<blank>' + originalCorrectAnswerContent + '</blank>');

    // Generate new passage based on original passage using Google GenAI
    const passageSystemInstruction = fs.readFileSync('./static/writing_prompts/punctuations/new_passage.txt', 'utf8');
    const result = await genAI.models.generateContent({
        model: "gemini-2.5-pro",
        config: {
            temperature: 0.55,
            systemInstruction: passageSystemInstruction,
            thinkingConfig: {
                thinkingBudget: 128,
            },
            tools: [{googleSearch:{}}],
        },
        contents: [
            {
                role: "user",
                parts: [{ text: formattedPassage + "\n\nUse Grounding with Google Search." }]
            }
        ],
    });

    const newPassage = result.text!;

    // Process new passage to obtain new passage, choices, and correct answer
    const processResult = await processPassageAndChoices(newPassage, originalChoices);

    if ('error' in processResult) {
        throw new Error(processResult.error);
    }

    const { modifiedPassage, shuffledChoices, correctAnswerIndex } = processResult;

    // Generate explanation
    const explanation = await generateExplanation(modifiedPassage, shuffledChoices, correctAnswerIndex, data.rationale);

    // Create the question object
    const questionObject = {
        passage: modifiedPassage,
        choices: shuffledChoices,
        correctAnswer: correctAnswerIndex,
        explanation: explanation,
        difficulty: data.difficulty
    };

    // Validate the question object before returning
    if (!isQuestionValid(questionObject)) return false;

    return questionObject;
}

async function generateExplanation(passage: string, choices: string[], correctAnswerIndex: number, rationale: string) {
    const explanationSystemInstruction = fs.readFileSync('./static/writing_prompts/punctuations/explanation.txt', 'utf8');
    const config = {
        thinkingConfig: {
        thinkingBudget: 0,
        },
        responseMimeType: 'text/plain',
        systemInstruction: [
            {
                text: explanationSystemInstruction,
            }
        ],
    };
    const model = 'gemini-2.5-flash';
    const contents = [
        {
            role: 'user',
            parts: [
                {
                text: `
                    ${passage}\n
                    Which choice completes the text so that it conforms to the conventions of Standard English?\n
                    ${choices.map((choice, index) => `${String.fromCharCode(65 + index)}. ${choice}`).join('\n')}\n
                    Correct answer: ${String.fromCharCode(65 + correctAnswerIndex)}\n\n
                    Example Explanation:\n\n
                    ${rationale.replace(/Choice [A-D] is the best answer\. /g, '')}
                `,
                },
            ],
        },
    ];

    const response = await genAI.models.generateContent({
        model,
        config,
        contents,
    });


    return response.text;
}

