<script lang="ts">
	import { user } from '$lib/firebase/auth.svelte.ts';
	import type { Question, Difficulty, QuestionType, CompletedQuestion } from '$lib/types/index';
	import { H5, Button, Checkbox, P1, P2, PracticeQuestion } from '$lib/ui';
	import posthog from 'posthog-js';
	import { innerWidth } from 'svelte/reactivity/window';
	import { db } from '$lib/firebase/firestore';
	import { doc, arrayUnion, increment, arrayRemove, onSnapshot, writeBatch, WriteBatch } from 'firebase/firestore';
	import Dropdown from '$lib/ui/Dropdown.svelte';
	import { browser } from '$app/environment';
	import { onDestroy, onMount } from 'svelte';
	import { debounce } from '$lib/utilities.ts';
	import { fade, fly } from 'svelte/transition';
	import { cubicIn, cubicOut } from 'svelte/easing';
	import { incrementMissionProgress } from '$lib/missions/missionEngine.ts';
	import { MissionMetric } from '$lib/types/mission.types.ts';

	let { data } = $props();

	// Initialize difficulty filters with their states and colors
	let difficulties = $state<{ label: Difficulty, checked: boolean, color: string }[]>([
		{ label: 'Easy', checked: false, color: 'var(--aquamarine)' },
		{ label: 'Medium', checked: false, color: 'var(--yellow)' },
		{ label: 'Hard', checked: false, color: 'var(--rose)' }
	]);

	// Initialize question type filters
	let questionTypes = $state<{ label: QuestionType | "All", checked: boolean }[]>([
		{ label: 'All', checked: true },
		{ label: 'Word in Context', checked: false },
		{ label: 'Main Idea', checked: false },
		{ label: 'Specific Detail', checked: false },
		{ label: 'Main Purpose', checked: false },
		{ label: 'Overall Structure', checked: false },
		{ label: 'Inference', checked: false },
		{ label: 'Command of Evidence', checked: false },
		{ label: 'Paired Passage', checked: false },
	]);
	
	// Effect to automatically check "All" when all individual question type is selected
	$effect(() => {
		if (questionTypes.slice(1).some(type => type.checked)) {
			questionTypes[0].checked = false;
		}
	})

	$effect(() => {
		if (questionTypes[0].checked) {
			questionTypes.slice(1).forEach(type => type.checked = false);
		}
	})

	type DropdownText = "All Questions" | "Questions I Have Not Encountered" | "Questions I Got Wrong" | "Questions Marked for Review";

	// Snapshot for preserving state between page navigations
	export const snapshot = {
		capture: () => {
			return {
				difficulties,
				questionTypes,
				dropdownText,
			}
		},
		restore: (value) => {
			difficulties = value.difficulties;
			questionTypes = value.questionTypes;
			dropdownText = value.dropdownText;
		}
	};

	// Component state variables
	let loading = $state(false);
	let questionCount = $state(0);
	let question: Question | null = $state(null);
	let showCorrectAnswer = $state(false);
	let isPopupOpen = $state(false);
	let questionScene: HTMLDivElement | null = $state(null);
	let answerState = $state<'correct' | 'incorrect' | 'unanswered'>('unanswered');
	let isAnswerUnanswered = $derived(answerState === 'unanswered');
	let isExplanationVisible = $state(false);
	let wasAnswersCorrect = $state<boolean[]>([]);
	let resetQuestion = $state(false);

	const dropdownChoices: DropdownText[] = ["All Questions", "Questions I Have Not Encountered", "Questions I Got Wrong", "Questions Marked for Review"];
	let dropdownText = $state(dropdownChoices[0]);
	let dropdownChoiceIndex = $derived(dropdownChoices.indexOf(dropdownText));
	
	// Track user progress with PostHog analytics
	$effect(() => {
		if (wasAnswersCorrect.length === 5) {
			posthog.capture('5_questions_completed');
		}

		if (wasAnswersCorrect.length === 7) {
			posthog.capture('7_questions_completed');
		}
	})

	// Toggle difficulty selection
	function toggleDifficulty(i: number) {
		difficulties[i].checked = !difficulties[i].checked;
	}

	let batch = $state<WriteBatch>(writeBatch(db));

	// Debounced Firestore update for Marked for Review
	async function updateMarkedForReview(marked: boolean) {
		if ($user && question) {
			try {
				const completedQuestionsRef = doc(db, 'users', $user.uid, 'completedQuestions', "dataDoc");
				const update = marked
					? { markedQuestions: arrayUnion(question.id) }
					: { markedQuestions: arrayRemove(question.id) };
				batch.update(completedQuestionsRef, update);
			} catch (e) {
				console.error('Failed to update marked for review:', e);
			}
		}
	}

	const debouncedUpdateMarkedForReview = debounce(updateMarkedForReview, 900);
	const debouncedScrollIntoView = debounce(() => questionScene?.scrollIntoView(true), 100);

	// Fetch a new question from the API based on selected filters
	async function start() {
		loading = true;
		const chosenDifficulties = difficulties.filter(d => d.checked).map(d => d.label);
		let chosenTypes: QuestionType[] = [];
		
		if (questionTypes[0].checked) {
			chosenTypes = questionTypes.filter(t => t.label !== "All").map(t => t.label as QuestionType);
		} else {
			chosenTypes = questionTypes.filter(t => t.checked && t.label !== "All").map(t => t.label as QuestionType);
		}

		const headers: Record<string, string> = {
			'Content-Type': 'application/json'
		};

		if ($user) {
			headers['Authorization'] = `Bearer ${await $user.getIdToken()}`;
		}

		const body: {
			chosenDifficulties: Difficulty[],
			chosenTypes: QuestionType[],
			excludedQuestionIds?: number[],
			questionIds?: number[]
		} = {
			chosenDifficulties, 
			chosenTypes,
		}

		switch(dropdownChoiceIndex) {
			case 1:
				body.excludedQuestionIds = completedQuestions
				break;
			case 2:
				body.questionIds = incorrectlyAnsweredQuestions
				break;
			case 3:
				body.questionIds = markedQuestions
				break;
		};

		const response = await fetch('/api/get-question', {
			method: 'POST',
			headers,
			body: JSON.stringify(body)
		});

		if (response.ok) {
			const res = await response.json();
			if (res.question) {
				question = res.question;
				isMarked = markedQuestions.includes(question.id);
			} else {
				alert(res.message);
			}
			questionCount += 1;
		} else if (response.status === 429) {
			isPopupOpen = true;
		} else {
			question = null;
			alert((await response.json()).message);
		}

		loading = false;
	}

	$effect(() => {
		if (question) {
			questionScene?.scrollIntoView(true);
			document.body.style.overflow = 'hidden';
		} else {
			document.body.style.overflow = '';
		}
	})

	$effect(() => {
		if (questionScene && innerWidth.current) {
			debouncedScrollIntoView();
		}
	})

	// Handle answer selection and update state
	async function handleAnswerSelect(index: number) {
		if (!isAnswerUnanswered) return;
		const isCorrect = index === question?.correctAnswer;
		answerState = isCorrect ? 'correct' : 'incorrect';
		wasAnswersCorrect = [...wasAnswersCorrect, isCorrect];


		// Track user progress in users/{id}/completedQuestions
		if ($user) {
			try {
				// Store the question data in a doc to reduce the number of reads. Kinda cope but will change if we get enough users.
				const completedQuestionsRef = doc(db, 'users', $user.uid, 'completedQuestions', "dataDoc");

				const newDocData = {
					data: arrayUnion({
						question: question.id,
						wasAnswerCorrect: isCorrect,
						studentAnswer: index,
						timestamp: new Date().toISOString(),
					}),
					[`stats.${question.questionType}.total`]: increment(1),
					[`stats.${question.questionType}.correct`]: isCorrect ? increment(1) : increment(0),
					incorrectlyAnsweredQuestions: isCorrect ? arrayRemove(question.id) : arrayUnion(question.id),
				}

				batch.update(completedQuestionsRef, newDocData);

				// Track mission progress for questions answered
				try {
					await incrementMissionProgress($user.uid, MissionMetric.QUESTIONS_ANSWERED, 1);
				} catch (missionError) {
					console.error('Failed to update mission progress:', missionError);
					// Don't throw - mission tracking shouldn't break question flow
				}
			} catch (e) {
				console.error('Failed to add completedQuestion:', e);
			}
		}
	}

	// Toggle explanation visibility
	function showExplanation() {
		isExplanationVisible = !isExplanationVisible;
	}

	// Load next question and reset states
	async function nextQuestion() {
		batch.commit();
		batch = writeBatch(db);
		await start();
		isExplanationVisible = false;
		answerState = 'unanswered';
		showCorrectAnswer = false;
		resetQuestion = !resetQuestion;
		// Scroll question wrapper back to top
		const questionWrapper = document.querySelector('.question-wrapper');
		if (questionWrapper) {
			questionWrapper.scrollTop = 0;
		}
	}

	let isMarked = $state(false);

	// Update in realtime
	let dataDoc: CompletedQuestion | null = $state(null);
	let { data: completedQuestionData, stats, markedQuestions, incorrectlyAnsweredQuestions } = $derived(dataDoc ?? {
		data: [],
		stats: {},
		markedQuestions: [],
		incorrectlyAnsweredQuestions: []
	} as CompletedQuestion);
	let completedQuestions = $derived(completedQuestionData.map(d => d.question))
	
	$effect(() => {
		if (!browser || !$user) return;
		
		const completedQuestionsRef = doc(db, 'users', $user.uid, 'completedQuestions', 'dataDoc');
		return onSnapshot(completedQuestionsRef, (snapshot) => {
			dataDoc = snapshot.exists() ? (snapshot.data() as CompletedQuestion) : null;
		});
	});

	// Clean up the listener when the component is destroyed
	onDestroy(() => {
		batch.commit();
	});


	// Annotate
	import { selectedRange, selectedName } from '$lib/annotate/annotateManager';

	let isAnnotate = $state(false);
	let selectedText: string | null = $state(null);
	let annoText: string | null = $state(null);
	let progressContainer: HTMLDivElement | null = $state(null);
	let annoButton: HTMLButtonElement | null = $state(null);

	let annotateIntro = $state(null);
	let annotatePassage = $state(null);
	let annotatePassage2 = $state(null);
	let annotateExplanation = $state(null);
	let commentTextarea: HTMLTextAreaElement | null = $state(null);
	let annotateElements = $derived({
		intro: annotateIntro,
		passage: annotatePassage,
		passage2: annotatePassage2,
		explanation: annotateExplanation
	});

	// Alert if no selection is made when annotate is clicked
	const annotateNoSelectionAlert = debounce(() => {
		isAnnotate = false;
	}, 1500);


	function truncateText(text: string | null): string {
		if (!text) return '';
		if (text.length >= 100) {
			let l = 39,
				r = text.length - 40;
			let ar = [' ', '.', ',', ';'];
			while (!ar.includes(text[l])) l++;
			while (!ar.includes(text[r])) r--;
			text = text.slice(0, l) + '...' + text.slice(r + 1);
		}
		return text;
	}

	function openAnnotate() {
		isAnnotate = true;
		annoText = annotateElements[$selectedName]?.getAnnoText();
		selectedText = annotateElements[$selectedName]?.getSelectedText();
	}

	function closeAnnotate() {
		isAnnotate = false;
		annoText = null;
		selectedText = null;
		annotateElements[$selectedName]?.setSelected(null, null);
		selectedName.set(null);
	}

	function onAnnotateClick() {
		if (!isAnnotate) {
			for(const key in annotateElements) {
				if (annotateElements[key]?.highlight()) {
					openAnnotate();
					return;
				}
			}
			isAnnotate = true;
			annotateNoSelectionAlert();
		} else {
			closeAnnotate();
		}
	}

	function onDeleteClick() {
		annotateElements[$selectedName]?.deleteHighlight();
	}

	// Update annotate text for current selected highlight
	$effect(() => {
		if (annoText !== null) {
			annotateElements[$selectedName]?.setAnnoText(annoText);
		}
	})

	// Open annotate when a highlight is selected
	$effect(() => {
		if ($selectedRange[0]) {
			openAnnotate();
		}
	});

	// Focus textarea when annotate panel opens
	$effect(() => {
		if (isAnnotate && commentTextarea) {
			// Use setTimeout to ensure the element is rendered and transition has started
			setTimeout(() => {
				commentTextarea?.focus();
			}, 100);
		}
	});

	// Named click handler for cleanup
	function handleOutsideClick(event: MouseEvent) {
		const target = event.target as HTMLElement;

		// Ignore clicks on <mark> elements
		if (target.tagName === "MARK") return;

		// Ignore clicks on annotate button
		if (annoButton && annoButton.contains(target)) return;

		if (!progressContainer?.contains(target)) {
			closeAnnotate();
		}
	}
</script>

<svelte:document onclick={handleOutsideClick} />

{#if !question}
<!-- Initial question bank setup screen -->
<div class="qb-root">
	<!-- Difficulty selection row -->
	<div class="qb-difficulty-row">
		<P1 isBold={true}>Difficulty:</P1>
		{#each difficulties as difficulty, i}
			<Button 
				isSecondary={!difficulty.checked} 
				onclick={() => toggleDifficulty(i)}
				--button-bg-color={difficulty.color}
			>
				{difficulty.label}
			</Button>
		{/each}
	</div>

	<div class="dropdown-row">
		<P1 isBold={true}>Include:</P1>
		<Dropdown bind:dropdownText {dropdownChoices} />
	</div>

	<!-- Question type and topic selection containers -->
	<div class="qb-selectors-row">
		<div class="qb-selector">
			<div class="qb-selector-header">
				<P1 isBold={true}>Select Question Type(s)</P1>
				<P1 isBold={true}>Correct Rate</P1>
			</div>
			<hr />
			<div class="qb-selector-list">
				{#each questionTypes as type, i}
					{@const stat = stats?.[type.label]}
					<div class="qb-checkbox-row">
						<Checkbox label={type.label} bind:isChecked={type.checked} />
						{#if i === 0}
							{@const allCorrect = Object.values(stats).reduce((acc, stat) => acc + stat.correct, 0)}
							{@const allTotal = Object.values(stats).reduce((acc, stat) => acc + stat.total, 0)}
							{@const allPercentage = Math.round(allCorrect / allTotal  * 100)}
							<P2>{allPercentage ? (allPercentage + "%") : "~" }</P2>		
						{:else}
							<P2>{stat?.total ? (Math.round((stat.correct / stat.total) * 100) + "%") : "~"}</P2>
						{/if}
					</div>
				{/each}
			</div>
		</div>
	</div>

	<Button disabled={loading} onclick={start}>Start!</Button>
</div>
{:else}
<!-- Question practice screen -->
<div class="qb-root question-scene" bind:this={questionScene}>
	<PracticeQuestion
		i={questionCount}
		{...question}
		onAnswerSelect={handleAnswerSelect}
		reset={resetQuestion}
		showCorrectAnswer={showCorrectAnswer}
		showExplanation={isExplanationVisible}
		updateMarkedInDB={() => debouncedUpdateMarkedForReview(isMarked)}
		bind:isMarked
		{onAnnotateClick}
		bind:annotateIntro
		bind:annotatePassage
		bind:annotatePassage2
		bind:annotateExplanation
		bind:annoButton
		onBackClick={() => {question = null}}
	/>
	<!-- Progress and navigation container -->
	{#if innerWidth.current > 1024}
		<div class="progress-container" bind:this={progressContainer}>
			{#if !isAnnotate}
				{@render progress()}
				{@render navButtons()}
			{:else}
				{@render annotatePanel()}
			{/if}
		</div>
	{:else}
		<div class="nav-container bg-white flex flex-row items-center justify-between gap-4 p-2 w-full border-t-[1px] border-solid border-black">
			<div>
				<svg xmlns="http://www.w3.org/2000/svg" width="149" height="30" viewBox="0 0 149 30" fill="none">
					<path d="M118.603 0.39924V29.6008H111.559V6.98669H111.388L104.857 10.9791V4.87643L112.058 0.39924H118.603Z" fill="url(#paint0_linear_4380_2617)"/>
					<path d="M136.764 30C135.168 30 133.637 29.7433 132.173 29.23C130.709 28.7072 129.407 27.8802 128.266 26.749C127.126 25.6084 126.227 24.116 125.572 22.2719C124.916 20.4183 124.592 18.1606 124.602 15.499C124.611 13.0941 124.906 10.9363 125.486 9.02567C126.066 7.10551 126.893 5.47529 127.967 4.13498C129.051 2.79468 130.343 1.77281 131.845 1.06939C133.357 0.356464 135.044 0 136.907 0C138.951 0 140.752 0.39924 142.311 1.19772C143.88 1.98669 145.134 3.05133 146.075 4.39163C147.016 5.72243 147.572 7.20532 147.744 8.8403H140.8C140.591 7.91825 140.13 7.21958 139.417 6.7443C138.713 6.2595 137.877 6.01711 136.907 6.01711C135.12 6.01711 133.784 6.79182 132.9 8.34125C132.026 9.89068 131.579 11.9629 131.56 14.558H131.745C132.145 13.6835 132.72 12.9325 133.471 12.3051C134.222 11.6778 135.082 11.1977 136.052 10.865C137.031 10.5228 138.067 10.3517 139.16 10.3517C140.909 10.3517 142.454 10.7557 143.794 11.5637C145.134 12.3717 146.185 13.4791 146.945 14.8859C147.706 16.2833 148.081 17.885 148.072 19.6911C148.081 21.7253 147.606 23.5219 146.646 25.0808C145.686 26.6302 144.355 27.8375 142.653 28.7025C140.961 29.5675 138.998 30 136.764 30ZM136.722 24.5817C137.587 24.5817 138.361 24.3774 139.046 23.9686C139.73 23.5599 140.267 23.0038 140.657 22.3004C141.047 21.597 141.237 20.8032 141.227 19.9192C141.237 19.0257 141.047 18.2319 140.657 17.538C140.277 16.8441 139.745 16.2928 139.06 15.884C138.385 15.4753 137.611 15.2709 136.736 15.2709C136.099 15.2709 135.505 15.3897 134.954 15.6274C134.402 15.865 133.922 16.1977 133.514 16.6255C133.114 17.0437 132.801 17.538 132.572 18.1084C132.344 18.6692 132.226 19.2776 132.216 19.9335C132.226 20.7985 132.425 21.5827 132.815 22.2861C133.205 22.9895 133.737 23.5504 134.412 23.9686C135.087 24.3774 135.857 24.5817 136.722 24.5817Z" fill="url(#paint1_linear_4380_2617)"/>
					<path d="M10.344 28.9314H0V0.94657H10.3303C13.1816 0.94657 15.6367 1.50681 17.6955 2.6273C19.7633 3.73867 21.3575 5.34197 22.478 7.43718C23.5985 9.52329 24.1587 12.0193 24.1587 14.9253C24.1587 17.8404 23.5985 20.3455 22.478 22.4407C21.3666 24.536 19.777 26.1438 17.7091 27.2643C15.6412 28.3757 13.1862 28.9314 10.344 28.9314ZM6.7639 23.165H10.0844C11.6512 23.165 12.9767 22.9008 14.0607 22.3724C15.1539 21.835 15.9783 20.965 16.534 19.7625C17.0988 18.5509 17.3812 16.9385 17.3812 14.9253C17.3812 12.9121 17.0988 11.3088 16.534 10.1154C15.9692 8.91294 15.1357 8.04753 14.0334 7.51917C12.9402 6.9817 11.592 6.71297 9.98871 6.71297H6.7639V23.165Z" fill="black"/>
					<path d="M43.2343 9.33654C43.1432 8.33448 42.7378 7.55561 42.0182 6.99992C41.3076 6.43512 40.2919 6.15272 38.971 6.15272C38.0965 6.15272 37.3677 6.2666 36.7847 6.49434C36.2017 6.72208 35.7644 7.03636 35.4729 7.43718C35.1814 7.8289 35.0311 8.27982 35.022 8.78996C35.0038 9.20901 35.0857 9.57795 35.2679 9.89678C35.4592 10.2156 35.7325 10.498 36.0878 10.744C36.4522 10.9808 36.8895 11.1904 37.3996 11.3725C37.9097 11.5547 38.4836 11.7142 39.1213 11.8508L41.5263 12.3974C42.9109 12.698 44.1316 13.0988 45.1883 13.5998C46.2542 14.1009 47.1469 14.6976 47.8666 15.3899C48.5953 16.0822 49.1465 16.8793 49.52 17.7812C49.8935 18.683 50.0848 19.6942 50.0939 20.8147C50.0848 22.5819 49.6384 24.0987 48.7548 25.3649C47.8711 26.6312 46.6003 27.6014 44.9424 28.2755C43.2935 28.9496 41.3031 29.2866 38.971 29.2866C36.6298 29.2866 34.5893 28.9359 32.8493 28.2345C31.1094 27.533 29.7566 26.4672 28.791 25.037C27.8254 23.6068 27.3289 21.7985 27.3016 19.6122H33.7785C33.8332 20.5141 34.0746 21.2656 34.5027 21.8668C34.9309 22.4681 35.5185 22.9236 36.2654 23.2333C37.0215 23.543 37.8961 23.6979 38.889 23.6979C39.8 23.6979 40.5743 23.5749 41.212 23.3289C41.8588 23.083 42.3552 22.7414 42.7014 22.3041C43.0476 21.8668 43.2252 21.3658 43.2343 20.801C43.2252 20.2727 43.0612 19.8217 42.7424 19.4482C42.4236 19.0656 41.9316 18.7377 41.2666 18.4644C40.6107 18.182 39.7727 17.9224 38.7524 17.6855L35.8282 17.0023C33.405 16.4466 31.4966 15.5493 30.1028 14.3104C28.709 13.0624 28.0167 11.3771 28.0258 9.25456C28.0167 7.52372 28.4813 6.00697 29.4196 4.70429C30.3578 3.40161 31.656 2.38589 33.3139 1.65712C34.9719 0.928351 36.8621 0.563965 38.9847 0.563965C41.1528 0.563965 43.0339 0.932905 44.6281 1.67078C46.2314 2.39956 47.4749 3.42439 48.3585 4.74529C49.2421 6.06618 49.693 7.5966 49.7113 9.33654H43.2343Z" fill="black"/>
					<path d="M58.6991 28.9314H51.4296L60.8717 0.94657H69.8766L79.3187 28.9314H72.0492L65.4766 7.99743H65.258L58.6991 28.9314ZM57.7289 17.9178H72.9237V23.0556H57.7289V17.9178Z" fill="black"/>
					<path d="M77.9215 6.43968V0.94657H101.575V6.43968H93.0891V28.9314H86.4208V6.43968H77.9215Z" fill="black"/>
					<defs>
						<linearGradient id="paint0_linear_4380_2617" x1="150.125" y1="-5.86693" x2="116.834" y2="-10.7627" gradientUnits="userSpaceOnUse">
						<stop stop-color="#66E2FF"/>
						<stop offset="1" stop-color="#FF66C4"/>
						</linearGradient>
						<linearGradient id="paint1_linear_4380_2617" x1="150.125" y1="-5.86693" x2="116.834" y2="-10.7627" gradientUnits="userSpaceOnUse">
						<stop stop-color="#66E2FF"/>
						<stop offset="1" stop-color="#FF66C4"/>
						</linearGradient>
					</defs>
				</svg>
			</div>
			{@render navButtons()}
		</div>
		{#if isAnnotate}
			<div class="annotate-overlay fixed top-0 left-0 right-0 bottom-0 z-40 bg-black/50"
				 in:fade={{ duration: 300 }}
				 out:fade={{ duration: 200 }}></div>
			<div class="annotate-panel fixed left-0 right-0 bottom-0 z-50 h-[50vh] bg-white flex flex-col p-4 gap-4 border-[1px] border-solid border-black rounded-t-[12px]"
				 bind:this={progressContainer}
				 in:fly={{ y: 200, duration: 300, easing: cubicOut, opacity: 1 }}
				 out:fly={{ y: 200, duration: 250, easing: cubicIn, opacity: 1 }}>
				{@render annotatePanel()}
			</div>
		{/if}
	{/if}
</div>
{/if}

<!-- Navigation buttons snippet -->
{#snippet navButtons()}
	<div class="nav-buttons">
		<!-- Show explanation button -->
		<Button --button-bg-color=var(--sky-blue) disabled={isAnswerUnanswered} onclick={showExplanation} title={isAnswerUnanswered ? "Answer the question to view explanation" : "View explanation"}>
			<svg class="svg-icon" width="32" height="32" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
				<path d="M281.674 253.424c11.553-14.585 24.288-28.14 38.208-40.267l-68.978-69.013c-10.828-10.867-28.404-10.867-39.275 0-10.831 10.831-10.831 28.404 0 39.235l70.046 70.043zM506.013 142.469c0.268 0 0.494 0 0.802 0 9.109 0 18.109 0.571 26.955 1.487l0-93.986c0-15.327-12.432-27.757-27.76-27.757s-27.721 12.429-27.721 27.757l0 93.968c8.846-0.895 17.842-1.469 26.956-1.469 0.27 0 0.534 0 0.762 0zM730.414 253.388l69.971-70.007c10.866-10.831 10.866-28.404 0-39.235-10.794-10.867-28.405-10.867-39.277 0l-69.018 69.013c13.958 12.123 26.695 25.664 38.324 40.227zM213.494 450.225c0-13.403 1.07-26.519 2.633-39.483l-98.682 0c-15.289 0-27.722 12.432-27.722 27.757 0 15.328 12.434 27.757 27.722 27.757l96.393 0c-0.229-5.28-0.345-10.581-0.345-16.033zM894.563 410.745l-98.643 0c1.6 12.964 2.633 26.082 2.633 39.483 0 5.45-0.115 10.753-0.305 16.033l96.315 0c15.332 0 27.722-12.429 27.722-27.757s-12.39-27.757-27.722-27.757zM745.017 638.262c-11.286 16.547-23.218 30.843-34.695 43.77l50.787 50.829c10.868 10.831 28.483 10.831 39.277 0 10.866-10.867 10.866-28.404 0-39.275l-55.37-55.324zM211.627 693.585c-10.831 10.867-10.831 28.404 0 39.275 10.868 10.831 28.448 10.831 39.275 0l50.83-50.863c-11.475-12.889-23.412-27.222-34.699-43.731l-55.403 55.324zM760.039 450.225c0.037-57.556-19.138-110.785-51.438-153.414-28.33-37.442-66.806-66.802-111.379-84.036l0.229-1.222-19.026-5.586c-14.798-4.29-30.051-7.265-45.605-8.867l-2.211-0.246-0.075 0-0.079-0.019c-7.623-0.724-15.557-1.279-23.83-1.279l-1.218 0c-8.276 0-16.207 0.557-23.833 1.279l-0.115 0.019-2.286 0.246c-15.535 1.6-30.791 4.575-45.549 8.867l-18.416 5.374 0.19 1.222c-44.802 17.214-83.505 46.651-111.99 84.244-32.258 42.628-51.438 95.854-51.438 153.414 0 41.274 7.132 75.207 18.227 103.29 16.661 42.114 42.094 70.559 62.533 92.138 10.22 10.754 19.22 19.941 25.317 27.795 6.254 7.892 9.265 13.995 10.141 18.837 4.459 23.565 4.917 53.306 4.917 60.854l0 2.174c0 34.085 27.609 61.617 61.659 61.655l142.51 0c34.092-0.037 61.659-27.609 61.659-61.655l0-2.097c-0.037-7.474 0.458-37.291 4.919-60.893 0.608-3.278 2.058-7.017 4.917-11.591 4.88-7.97 14.11-17.883 25.548-29.819 17.083-17.959 38.815-40.604 56.091-72.805 17.312-32.161 29.703-73.706 29.626-127.882zM701.813 537.676c-13.614 34.336-34.053 57.575-53.763 78.391-9.841 10.411-19.487 20.095-27.911 30.812-8.311 10.562-15.745 22.65-18.606 37.481-5.377 28.94-5.607 59.978-5.638 68.862 0 1.181 0 1.828 0 2.096-0.037 10.295-8.314 18.606-18.608 18.606l-142.51 0c-5.223 0-9.761-2.059-13.194-5.45-3.391-3.434-5.412-7.934-5.412-13.154 0-0.268 0-0.953 0-2.172-0.037-8.957-0.306-39.921-5.682-68.783-1.828-9.797-5.834-18.571-10.674-26.348-8.579-13.612-19.559-24.819-30.889-36.832-17.116-17.842-35.232-37.119-49.341-63.41-14.07-26.309-24.517-59.745-24.557-107.54 0.037-47.947 15.862-91.95 42.706-127.427 26.845-35.442 64.595-62.209 108.175-75.246l5.377-1.638c10.524-2.708 21.315-4.861 32.429-6.026l0.075 0 2.06-0.229c6.519-0.627 12.847-1.03 19.103-1.066l1.105 0.115 1.106-0.076c6.212 0 12.579 0.42 19.063 1.03l-0.079 0 2.137 0.229 0.037 0c11.093 1.163 21.887 3.277 32.372 6.026l5.451 1.638c43.582 13.037 81.329 39.807 108.175 75.246 26.801 35.478 42.666 79.482 42.666 127.427 0.003 36.409-6.094 64.511-15.169 87.446zM532.782 197.107l0.079 0zM479.17 197.107l0 0c0 0 0.037 0 0.075 0l-0.075 0zM576.706 833.825l-141.369 0c-14.604 0-26.499 11.822-26.499 26.539 0 14.564 11.899 26.465 26.499 26.465l141.369 0c14.604 0 26.499-11.899 26.499-26.465 0-14.721-11.899-26.539-26.499-26.539zM576.706 900.704l-141.369 0c-14.604 0-26.499 11.857-26.499 26.464 0 14.678 11.899 26.539 26.499 26.539l141.369 0c14.604 0 26.499-11.857 26.499-26.539 0-14.605-11.899-26.464-26.499-26.464zM518.406 968.116l-72.805 0c0 0.801-0.154 1.561-0.154 2.404 0 14.643 22.498 26.499 43.297 26.499l34.546 0c20.818 0 43.277-11.857 43.277-26.499 0-0.837-0.115-1.6-0.115-2.404l-48.044 0z"  />
			</svg>
		</Button>

		<!-- Next question button -->
		<Button --button-bg-color=var(--aquamarine) disabled={isAnswerUnanswered || loading} onclick={nextQuestion} title={isAnswerUnanswered ? "Answer the question to continue" : loading ? "Loading next question..." : "Next question"}>
			<svg width="32" height="32" viewBox="0 0 37 36" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path d="M23.6431 16.9353L15.1531 8.4603C15.0137 8.31971 14.8478 8.20812 14.665 8.13196C14.4822 8.05581 14.2861 8.0166 14.0881 8.0166C13.8901 8.0166 13.694 8.05581 13.5112 8.13196C13.3284 8.20812 13.1625 8.31971 13.0231 8.4603C12.7437 8.74134 12.5869 9.12152 12.5869 9.5178C12.5869 9.91408 12.7437 10.2943 13.0231 10.5753L20.4481 18.0753L13.0231 25.5003C12.7437 25.7813 12.5869 26.1615 12.5869 26.5578C12.5869 26.9541 12.7437 27.3343 13.0231 27.6153C13.162 27.757 13.3277 27.8698 13.5105 27.947C13.6933 28.0243 13.8896 28.0645 14.0881 28.0653C14.2866 28.0645 14.4829 28.0243 14.6657 27.947C14.8485 27.8698 15.0142 27.757 15.1531 27.6153L23.6431 19.1403C23.7954 18.9998 23.9169 18.8294 24 18.6396C24.0831 18.4499 24.126 18.245 24.126 18.0378C24.126 17.8306 24.0831 17.6257 24 17.436C23.9169 17.2462 23.7954 17.0758 23.6431 16.9353V16.9353Z" fill="black"/>
			</svg>
		</Button>
	</div>
{/snippet}

<!-- Progress tracking snippet -->
{#snippet progress()}
	<div class="progress">
		{#each wasAnswersCorrect as answer}
			{#if answer}
				<!-- Correct answer indicator -->
				<svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
					<g filter="url(#filter0_d_4249_5345)">
					<rect width="24" height="24" rx="4" fill="#55ECB2"/>
					<rect x="0.5" y="0.5" width="23" height="23" rx="3.5" stroke="black"/>
					<path d="M18.7099 7.20986C18.617 7.11613 18.5064 7.04174 18.3845 6.99097C18.2627 6.9402 18.132 6.91406 17.9999 6.91406C17.8679 6.91406 17.7372 6.9402 17.6154 6.99097C17.4935 7.04174 17.3829 7.11613 17.29 7.20986L9.83995 14.6699L6.70995 11.5299C6.61343 11.4366 6.49949 11.3633 6.37463 11.3141C6.24978 11.2649 6.11645 11.2408 5.98227 11.2431C5.84809 11.2454 5.71568 11.2741 5.5926 11.3276C5.46953 11.3811 5.35819 11.4583 5.26495 11.5549C5.17171 11.6514 5.0984 11.7653 5.04919 11.8902C4.99999 12.015 4.97586 12.1484 4.97818 12.2825C4.9805 12.4167 5.00923 12.5491 5.06272 12.6722C5.11622 12.7953 5.19343 12.9066 5.28995 12.9999L9.12995 16.8399C9.22291 16.9336 9.33351 17.008 9.45537 17.0588C9.57723 17.1095 9.70794 17.1357 9.83995 17.1357C9.97196 17.1357 10.1027 17.1095 10.2245 17.0588C10.3464 17.008 10.457 16.9336 10.55 16.8399L18.7099 8.67986C18.8115 8.58622 18.8925 8.47257 18.9479 8.34607C19.0033 8.21957 19.0319 8.08296 19.0319 7.94486C19.0319 7.80676 19.0033 7.67015 18.9479 7.54365C18.8925 7.41715 18.8115 7.3035 18.7099 7.20986Z" fill="black"/>
					</g>
					<defs>
					<filter id="filter0_d_4249_5345" x="0" y="0" width="28" height="28" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feFlood flood-opacity="0" result="BackgroundImageFix"/>
					<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
					<feOffset dx="4" dy="4"/>
					<feComposite in2="hardAlpha" operator="out"/>
					<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
					<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4249_5345"/>
					<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_4249_5345" result="shape"/>
					</filter>
					</defs>
				</svg>
			{:else}
				<!-- Incorrect answer indicator -->
				<svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
					<g filter="url(#filter0_d_4249_5340)">
					<rect width="24" height="24" rx="4" fill="#EB47AB"/>
					<rect x="0.5" y="0.5" width="23" height="23" rx="3.5" stroke="black"/>
					<path d="M13.4099 12.0002L17.7099 7.71019C17.8982 7.52188 18.004 7.26649 18.004 7.00019C18.004 6.73388 17.8982 6.47849 17.7099 6.29019C17.5216 6.10188 17.2662 5.99609 16.9999 5.99609C16.7336 5.99609 16.4782 6.10188 16.2899 6.29019L11.9999 10.5902L7.70994 6.29019C7.52164 6.10188 7.26624 5.99609 6.99994 5.99609C6.73364 5.99609 6.47824 6.10188 6.28994 6.29019C6.10164 6.47849 5.99585 6.73388 5.99585 7.00019C5.99585 7.26649 6.10164 7.52188 6.28994 7.71019L10.5899 12.0002L6.28994 16.2902C6.19621 16.3831 6.12182 16.4937 6.07105 16.6156C6.02028 16.7375 5.99414 16.8682 5.99414 17.0002C5.99414 17.1322 6.02028 17.2629 6.07105 17.3848C6.12182 17.5066 6.19621 17.6172 6.28994 17.7102C6.3829 17.8039 6.4935 17.8783 6.61536 17.9291C6.73722 17.9798 6.86793 18.006 6.99994 18.006C7.13195 18.006 7.26266 17.9798 7.38452 17.9291C7.50638 17.8783 7.61698 17.8039 7.70994 17.7102L11.9999 13.4102L16.2899 17.7102C16.3829 17.8039 16.4935 17.8783 16.6154 17.9291C16.7372 17.9798 16.8679 18.006 16.9999 18.006C17.132 18.006 17.2627 17.9798 17.3845 17.9291C17.5064 17.8783 17.617 17.8039 17.7099 17.7102C17.8037 17.6172 17.8781 17.5066 17.9288 17.3848C17.9796 17.2629 18.0057 17.1322 18.0057 17.0002C18.0057 16.8682 17.9796 16.7375 17.9288 16.6156C17.8781 16.4937 17.8037 16.3831 17.7099 16.2902L13.4099 12.0002Z" fill="black"/>
					</g>
					<defs>
					<filter id="filter0_d_4249_5340" x="0" y="0" width="28" height="28" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feFlood flood-opacity="0" result="BackgroundImageFix"/>
					<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
					<feOffset dx="4" dy="4"/>
					<feComposite in2="hardAlpha" operator="out"/>
					<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
					<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4249_5340"/>
					<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_4249_5340" result="shape"/>
					</filter>
					</defs>
				</svg>
			{/if}
		{/each}
	</div>
{/snippet}

<!-- Annotation Panel snippet -->
{#snippet annotatePanel()}
	<H5 --text-align="center">Annotate</H5>

	{#if $selectedRange[0]}
		<!-- Highlighted word -->
		<P2 --text-align="center">{truncateText(selectedText)}</P2>

		<!-- Comment section -->
		<div class="comment-section">
			<textarea
				class="comment-input"
				placeholder="Add your comment here..."
				bind:value={annoText}
				bind:this={commentTextarea}
				onkeydown={(e) => {
					if (e.key === 'Enter' && !e.shiftKey) {
						e.preventDefault();
						closeAnnotate();
					}
				}}
			></textarea>
		</div>

		<!-- Action buttons -->
		<div class="vocab-button-container flex justify-center gap-4">
			<!-- <Button>Add to vocab</Button> -->
			<!-- Confirm/Save button -->
			<button onclick={closeAnnotate} aria-label="Confirm and save annotation">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z" fill="#000"/>
				</svg>
			</button>
			<!-- Delete button -->
			<button onclick={onDeleteClick} aria-label="Delete annotation">
				<svg  width="30" height="30" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path d="M10 18C10.2652 18 10.5196 17.8946 10.7071 17.7071C10.8946 17.5196 11 17.2652 11 17V11C11 10.7348 10.8946 10.4804 10.7071 10.2929C10.5196 10.1054 10.2652 10 10 10C9.73478 10 9.48043 10.1054 9.29289 10.2929C9.10536 10.4804 9 10.7348 9 11V17C9 17.2652 9.10536 17.5196 9.29289 17.7071C9.48043 17.8946 9.73478 18 10 18ZM20 6H16V5C16 4.20435 15.6839 3.44129 15.1213 2.87868C14.5587 2.31607 13.7956 2 13 2H11C10.2044 2 9.44129 2.31607 8.87868 2.87868C8.31607 3.44129 8 4.20435 8 5V6H4C3.73478 6 3.48043 6.10536 3.29289 6.29289C3.10536 6.48043 3 6.73478 3 7C3 7.26522 3.10536 7.51957 3.29289 7.70711C3.48043 7.89464 3.73478 8 4 8H5V19C5 19.7956 5.31607 20.5587 5.87868 21.1213C6.44129 21.6839 7.20435 22 8 22H16C16.7956 22 17.5587 21.6839 18.1213 21.1213C18.6839 20.5587 19 19.7956 19 19V8H20C20.2652 8 20.5196 7.89464 20.7071 7.70711C20.8946 7.51957 21 7.26522 21 7C21 6.73478 20.8946 6.48043 20.7071 6.29289C20.5196 6.10536 20.2652 6 20 6ZM10 5C10 4.73478 10.1054 4.48043 10.2929 4.29289C10.4804 4.10536 10.7348 4 11 4H13C13.2652 4 13.5196 4.10536 13.7071 4.29289C13.8946 4.48043 14 4.73478 14 5V6H10V5ZM17 19C17 19.2652 16.8946 19.5196 16.7071 19.7071C16.5196 19.8946 16.2652 20 16 20H8C7.73478 20 7.48043 19.8946 7.29289 19.7071C7.10536 19.5196 7 19.2652 7 19V8H17V19ZM14 18C14.2652 18 14.5196 17.8946 14.7071 17.7071C14.8946 17.5196 15 17.2652 15 17V11C15 10.7348 14.8946 10.4804 14.7071 10.2929C14.5196 10.1054 14.2652 10 14 10C13.7348 10 13.4804 10.1054 13.2929 10.2929C13.1054 10.4804 13 10.7348 13 11V17C13 17.2652 13.1054 17.5196 13.2929 17.7071C13.4804 17.8946 13.7348 18 14 18Z" fill="#000"/>
				</svg>
			</button>
		</div>
	{:else}
		<div class="no-selection flex-grow-[1] flex justify-center items-center text-center">
			<P1>Select a word to annotate.</P1>
		</div>
	{/if}
{/snippet}

<style>
	.qb-root {
        min-height: 100vh;
        width: 100%;
		background: var(--very-light-sky-blue);
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding: 3rem 4rem;
		gap: 2rem;
	}

	.qb-difficulty-row {
		display: flex;
		flex-wrap: wrap;
		gap: 1rem;
		align-items: center;
		justify-content: start;
		width: 100%;
		max-width: 32rem;
	}

	.dropdown-row {
		display: inline-flex;
		gap: 1rem;
		align-items: center;
		width: 100%;
		max-width: 32rem;
	}

	.qb-selectors-row {
		width: 100%;
		display: flex;
		gap: 2rem;
		justify-content: center;
		flex-wrap: wrap;
	}

	.qb-selector {
		background: var(--white);
		border: 1px solid var(--pitch-black);
		border-radius: 1rem;
		box-shadow: 0.25rem 0.25rem 0px var(--pitch-black);
		padding: 2rem 2.5rem 2rem 2.5rem;
		width: 100%;
		height: fit-content;
		max-height: 20rem;
        max-width: 32rem;
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	.qb-selector-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 1.1rem;
	}

	hr {
		border: 1px solid var(--pitch-black);
	}

	.qb-selector-list {
		display: flex;
		flex-direction: column;
		gap: 0.75rem;
		height: 100%;
		overflow-y: auto;
		scrollbar-color: var(--sky-blue) var(--light-sky-blue);
		scrollbar-width: thin;
	}

	.qb-checkbox-row {
		display: flex;
		align-items: start;
		gap: 1rem;
		padding-right: 0.5rem;
	}

	.question-scene {
		display: inline-flex;
		flex-direction: row;
		justify-content: center;
		gap: 1rem;
		height: 100vh;
	}

	.progress-container {
		display: flex;
		flex-direction: column;
		gap: 1rem;
		width: 100%;
		max-width: 14rem;
		height: 100%;
		border: 1.5px solid var(--pitch-black);
		border-radius: 1rem;
		box-shadow: 0.25rem 0.25rem 0px var(--pitch-black);
		padding: 2rem 0.5rem;
		background: white;
	}

	.progress {
		display: inline-flex;
		flex-wrap: wrap;
		align-content: start;
		justify-content: center;
		gap: 0.25rem;
		padding: 0 1rem;
		height: 100%;
	}

	.nav-buttons {
		display: inline-flex;
		gap: 0.625rem;
		justify-content: center;
	}


	@media (max-width: 1200px) {		
		.progress-container {
			align-items: center;
			justify-content: space-between;
		}
	}

	@media (max-width: 960px) {
		.qb-selector {
			padding: 1.5rem 1rem;
		}

		.progress-container {
			padding: 1rem;
		}
	}

	@media (min-width: 960px) and (max-width: 1440px) {
		.question-scene {
			padding: 1rem;
		}
	}

	@media (max-width: 1024px) {
		.qb-root {
			padding: 1rem;
			min-height: calc(100vh - 4rem); 
		}

		.question-scene {
			padding: 0;
			justify-content: start;
			flex-direction: column;
			gap: 0;
		}

		.progress-container {
			display: flex;
			flex-direction: column;
			gap: 2rem;
			overflow-y: initial;
		}
	}

	/* For extremely big screen */
	@media (min-width: 1440px) {
		.progress-container {
			max-width: 17rem;
			max-height: 59rem;
		}
	}

	.comment-section {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 0.625rem;
		width: 100%
	}

	.comment-input {
		flex: 1;
		border: 1px solid var(--pitch-black, #000000);
		border-radius: 0.5rem;
		padding: 0.625rem;
		font-family: 'Open Sans', sans-serif;
		font-size: 18px;
		min-height: 100px;
	}

	.comment-input:focus {
		outline: none;
		box-shadow: 0 0 0 2px var(--sky-blue, #66e2ff);
	}
</style>
